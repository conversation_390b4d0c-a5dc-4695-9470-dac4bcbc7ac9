# Trading Bot with Telegram Interface

A cryptocurrency trading bot that analyzes market conditions and sends high-quality trading signals to users via Telegram.

## Features

- **Dual-Process Architecture**: Separate processes for the Telegram bot and trade checker ensure responsiveness
- **High-Quality Trade Analysis**: Sophisticated quality scoring system for finding the best trades
- **Automated Operation**: <PERSON><PERSON> automatically starts the trade checker and manages processes
- **User Management**: Approve users to receive trading signals
- **Persistent Storage**: All data stored in JSON files for reliability
- **Clean Terminal Display**: Real-time status updates in the terminal

## Project Structure

- `bot_server.py`: Main Telegram bot server
- `trade_checker.py`: Trade checking and analysis
- `run_trading_bot.bat`: Script to start the entire system

### Directories

- `config/`: Configuration files
- `data/`: Data storage (JSON files)
- `handlers/`: Telegram command handlers
- `logs/`: Log files
- `memory/`: Documentation of changes
- `models/`: Data models
- `services/`: Core services (Binance API, trading strategy, etc.)
- `utils/`: Utility functions

## How to Use

1. **Setup**:
   - Install required packages: `pip install -r requirements.txt`
   - Set up your Binance API keys in `.env` file
   - Configure bot token and owner ID in `config/settings.py`

2. **Running the Bot**:
   - Run `run_trading_bot.bat` to start the entire system
   - The bot will automatically start the trade checker

3. **Available Commands**:
   - `/start`: Start the bot (all users)
   - `/help`: Get help information (all users)
   - `/user`: Manage users (owner only)
   - `/status`: Check system status (owner only)

4. **Stopping the Bot**:
   - Press Ctrl+C to stop the bot
   - The trade checker will be stopped automatically

## Trade Analysis

The bot analyzes trading pairs using multiple factors:

1. **Quality Scoring (0-1 scale)**:
   - Volatility (lower is better)
   - Volume (higher is better)
   - Trend strength (stronger is better)
   - Risk/reward ratio (higher is better)
   - Market type (slight preference for futures)

2. **Trading Conditions**:
   - ICT (Institutional Candle Theory)
   - SMC (Smart Money Concepts)
   - PA (Price Action)
   - S/R (Support/Resistance)
   - Pattern recognition

## Architecture

The system uses a dual-process architecture:
- **Bot Server**: Handles Telegram interactions and user commands
- **Trade Checker**: Analyzes markets and finds trading opportunities
- **Shared Data**: Communication via JSON files

## Maintenance

- Check `logs/error.log` for any issues
- Review `memory/` directory for documentation of changes
- Use `/status` command to monitor the system
