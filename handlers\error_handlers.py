"""
Error handlers for the Telegram bot.
"""
import html
import json
import logging
import traceback
from telegram import Update
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from config.settings import OWNER_ID

logger = logging.getLogger(__name__)

async def error_handler(update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle errors in the dispatcher."""
    # Log the error
    logger.error("Exception while handling an update:", exc_info=context.error)

    # Get the error traceback
    tb_list = traceback.format_exception(None, context.error, context.error.__traceback__)
    tb_string = "".join(tb_list)

    # Build the message for the developer
    update_str = update.to_dict() if isinstance(update, Update) else str(update)
    message = (
        f"An exception occurred while processing an update:\n\n"
        f"<pre>update = {html.escape(json.dumps(update_str, indent=2, ensure_ascii=False))}</pre>\n\n"
        f"<pre>context.chat_data = {html.escape(str(context.chat_data))}</pre>\n\n"
        f"<pre>context.user_data = {html.escape(str(context.user_data))}</pre>\n\n"
        f"<pre>{html.escape(tb_string)}</pre>"
    )

    # Split the message if it's too long
    if len(message) > 4096:
        for i in range(0, len(message), 4096):
            await context.bot.send_message(
                chat_id=OWNER_ID,
                text=message[i:i+4096],
                parse_mode=ParseMode.HTML
            )
    else:
        # Send the error message to the developer
        await context.bot.send_message(
            chat_id=OWNER_ID,
            text=message,
            parse_mode=ParseMode.HTML
        )

    # If the error happened in a callback query, answer it to prevent the "loading" icon
    if update and isinstance(update, Update) and update.callback_query:
        await update.callback_query.answer(
            text="An error occurred. The developer has been notified.",
            show_alert=True
        )
    
    # If the error happened in a message, reply to it
    elif update and isinstance(update, Update) and update.effective_message:
        await update.effective_message.reply_text(
            "An error occurred while processing your request. "
            "The developer has been notified."
        )
