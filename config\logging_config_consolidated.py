"""
Consolidated logging configuration for the trading bot.
This combines functionality from both logging_config.py and logging_config_new.py.
"""
import os
import logging
from logging.handlers import RotatingFileHandler

# Import terminal display functions are now handled directly where needed

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

def configure_logging():
    """Configure logging for the application."""
    # Create a logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create a formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Create a console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # Create an error file handler (rotating, max 10MB, keep 5 backups)
    error_handler = RotatingFileHandler(
        'logs/error.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)

    # Create an info file handler (rotating, max 10MB, keep 5 backups)
    info_handler = RotatingFileHandler(
        'logs/info.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    info_handler.setLevel(logging.INFO)
    info_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(console_handler)
    logger.addHandler(error_handler)
    logger.addHandler(info_handler)

    return logger

# Note: All display functions are now directly imported from terminal_display_consolidated
# No need to redefine them here
