import logging
import uuid
import re
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from typing import Dict, List, Any, Optional

from config.settings import OWNER_ID, OWNER_USER_NAME
from models.database import (
    is_user_approved, is_user_rejected, approve_user, reject_user,
    get_approved_users, get_rejected_users, remove_approved_user,
    remove_rejected_user, record_trade, get_user_data, can_receive_trade,
    save_active_signal, get_active_signal, update_active_signal
)
from models.trade import TradeSignal

from utils.shared_data_manager import (
    load_shared_data, save_shared_data, load_waiting_trades,
    load_active_trades, add_command, is_trade_checker_running
)

logger = logging.getLogger(__name__)

async def handle_callback_query(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle callback queries from inline buttons."""
    query = update.callback_query
    await query.answer()

    # Get the callback data
    callback_data = query.data

    # Handle different callback types
    if callback_data.startswith("approve_"):
        await handle_approve_callback(update, context, callback_data)
    elif callback_data.startswith("reject_"):
        await handle_reject_callback(update, context, callback_data)
    elif callback_data.startswith("get_trade_"):
        await handle_get_trade_callback(update, context, callback_data)
    elif callback_data == "list_approved_users":
        await handle_list_approved_users(update, context)
    elif callback_data == "list_rejected_users":
        await handle_list_rejected_users(update, context)
    elif callback_data.startswith("user_approved_"):
        await handle_user_approved_callback(update, context, callback_data)
    elif callback_data.startswith("user_rejected_"):
        await handle_user_rejected_callback(update, context, callback_data)
    elif callback_data.startswith("message_user_"):
        await handle_message_user_callback(update, context, callback_data)
    elif callback_data.startswith("move_to_rejected_"):
        await handle_move_to_rejected_callback(update, context, callback_data)
    elif callback_data.startswith("move_to_approved_"):
        await handle_move_to_approved_callback(update, context, callback_data)
    elif callback_data == "trade_spot":
        await handle_trade_spot_callback(update, context)
    elif callback_data == "trade_futures":
        await handle_trade_futures_callback(update, context)
    elif callback_data == "wait_better_trade":
        await handle_wait_better_trade_callback(update, context)

async def handle_approve_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
    """Handle approval of a user."""
    query = update.callback_query
    user_id = int(callback_data.split("_")[1])

    # Extract user information from the message text
    message_text = query.message.text

    # Parse the message to extract user details
    first_name = "Unknown"
    last_name = ""
    username = ""

    # Extract first name
    first_name_match = message_text.find("First Name: ")
    if first_name_match != -1:
        first_name_end = message_text.find("\n", first_name_match)
        if first_name_end != -1:
            first_name = message_text[first_name_match + 12:first_name_end].strip()
            if first_name == "N/A":
                first_name = "Unknown"

    # Extract last name
    last_name_match = message_text.find("Last Name: ")
    if last_name_match != -1:
        last_name_end = message_text.find("\n", last_name_match)
        if last_name_end != -1:
            last_name = message_text[last_name_match + 11:last_name_end].strip()
            if last_name == "N/A":
                last_name = ""

    # Extract username
    username_match = message_text.find("Username: @")
    if username_match != -1:
        username_end = message_text.find("\n", username_match)
        if username_end != -1:
            username = message_text[username_match + 11:username_end].strip()
            if username == "N/A":
                username = ""

    # Create complete user data
    user_data = {
        'id': user_id,
        'first_name': first_name,
        'last_name': last_name,
        'username': username,
        'approved_at': datetime.now().isoformat()
    }

    # Approve user
    approve_user(user_data)

    # Notify owner
    await query.edit_message_text(
        text=f"User {first_name} {last_name} (@{username if username else 'No username'}) has been approved. They can now receive signals."
    )

    # Notify user
    try:
        await context.bot.send_message(
            chat_id=user_id,
            text=(
                "🎉 Congratulations! Your access request has been approved.\n\n"
                "You can now receive trading signals. Make sure to turn on notifications "
                "to get alerts for new signals.\n\n"
                "Type /help to learn how to use the signals effectively."
            )
        )
    except Exception as e:
        logger.error(f"Failed to notify user {user_id}: {e}")

async def handle_reject_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
    """Handle rejection of a user."""
    query = update.callback_query
    user_id = int(callback_data.split("_")[1])

    # Reject user
    reject_user(user_id)

    # Notify owner
    await query.edit_message_text(
        text=f"User {user_id} has been rejected."
    )

    # Notify user
    try:
        await context.bot.send_message(
            chat_id=user_id,
            text=(
                "We're sorry, but your access request has been declined.\n\n"
                f"If you believe this is an error, please contact {OWNER_USER_NAME} for assistance."
            )
        )
    except Exception as e:
        logger.error(f"Failed to notify user {user_id}: {e}")

async def handle_get_trade_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
    """Handle a user accepting a trade signal."""
    query = update.callback_query
    user_id = update.effective_user.id

    # Check if user can receive a trade first
    can_receive, trades_remaining = can_receive_trade(user_id)
    if not can_receive:
        await query.edit_message_reply_markup(reply_markup=None)
        await context.bot.send_message(
            chat_id=user_id,
            text=(
                "❌ You have reached your daily trade limit.\n\n"
                "You can only accept 3 trades per day. Please try again tomorrow."
            )
        )
        return

    # Extract signal ID
    signal_id = callback_data.replace("get_trade_", "")

    # Parse signal ID to get symbol, direction, and timestamp
    parts = signal_id.split('_')
    if len(parts) >= 3:
        symbol = parts[0]
        direction = parts[1]
        timestamp = float(parts[2]) if len(parts) > 2 else 0
    else:
        symbol = "Unknown"
        direction = "Unknown"
        timestamp = 0

    # Extract trade details from the message
    original_message = query.message.text

    # Parse the message to extract entry price, stop loss, and targets
    entry_price = None
    stop_loss = None
    targets = []
    leverage = None

    # Extract entry price
    entry_match = re.search(r"Entry Price:\s*([0-9.]+)", original_message)
    if entry_match:
        entry_price = float(entry_match.group(1))

    # Extract stop loss
    stop_match = re.search(r"Stop Loss:\s*([0-9.]+)", original_message)
    if stop_match:
        stop_loss = float(stop_match.group(1))

    # Extract targets
    target_matches = re.findall(r"(\d+)\.\s*([0-9.]+)", original_message)
    for match in target_matches:
        targets.append(float(match[1]))

    # Extract leverage for futures trades
    if "FUTURES" in original_message:
        leverage_match = re.search(r"Recommended leverage:\s*([0-9-x]+)", original_message)
        if leverage_match:
            leverage = leverage_match.group(1)

    # Determine market type
    market_type = "SPOT"
    if "FUTURES" in original_message:
        market_type = "FUTURES"

    # Record the trade for this user
    trade_data = {
        "signal_id": signal_id,
        "symbol": symbol,
        "direction": direction,
        "timestamp": timestamp,
        "accepted_at": datetime.now().timestamp(),
        "user_id": user_id,
        "entry_price": entry_price,
        "stop_loss": stop_loss,
        "targets": targets,
        "leverage": leverage,
        "market_type": market_type,
        "status": "waiting_entry"
    }

    # Record the trade (this counts against the daily limit)
    success = record_trade(user_id, trade_data)

    if success:
        # Save the trade signal as active for this user
        signal_data = {
            "user_id": user_id,
            "symbol": symbol,
            "direction": direction,
            "timestamp": timestamp,
            "message_id": query.message.message_id,
            "chat_id": user_id,
            "status": "waiting_entry",
            "original_message": original_message,
            "entry_price": entry_price,
            "stop_loss": stop_loss,
            "targets": targets,
            "leverage": leverage,
            "market_type": market_type
        }

        # Save the active signal
        save_active_signal(signal_id, signal_data)

        # Add the trade to waiting trades
        from utils.shared_data_manager import add_waiting_trade
        add_waiting_trade(trade_data, market_type)

        # Remove the inline keyboard
        await query.edit_message_reply_markup(reply_markup=None)

        # Get updated trades remaining
        _, updated_trades_remaining = can_receive_trade(user_id)

        # Send confirmation message
        await context.bot.send_message(
            chat_id=user_id,
            text=(
                f"✅ You have successfully accepted the {direction} signal for {symbol}.\n\n"
                f"Make sure to follow the entry, targets, and stop loss levels carefully.\n"
                f"This counts as 1 of your 3 daily trades.\n\n"
                f"Trades remaining today: {updated_trades_remaining}\n\n"
                f"You will receive alerts when:\n"
                f"• Entry price is triggered\n"
                f"• Target prices are hit\n"
                f"• Stop loss is triggered\n\n"
                f"Good luck with your trade!"
            )
        )
    else:
        # This should rarely happen since we check at the beginning
        await query.edit_message_reply_markup(reply_markup=None)
        await context.bot.send_message(
            chat_id=user_id,
            text=(
                "❌ Error recording your trade.\n\n"
                "Please try again or contact the admin if the problem persists."
            )
        )

async def handle_list_approved_users(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle listing of approved users."""
    query = update.callback_query

    # Get approved users
    approved_users = get_approved_users()

    if not approved_users:
        await query.edit_message_text(
            text="There are no approved users."
        )
        return

    # Create keyboard with user buttons
    keyboard = []
    for user_id, user_data in approved_users.items():
        user_name = user_data.get('first_name', 'Unknown')
        username = user_data.get('username', '')
        display_name = f"{user_name} (@{username})" if username else user_name
        keyboard.append([InlineKeyboardButton(display_name, callback_data=f"user_approved_{user_id}")])

    # Add back button
    keyboard.append([InlineKeyboardButton("◀️ Back", callback_data="back_to_user_menu")])

    reply_markup = InlineKeyboardMarkup(keyboard)

    await query.edit_message_text(
        text="Select an approved user to manage:",
        reply_markup=reply_markup
    )

async def handle_list_rejected_users(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle listing of rejected users."""
    query = update.callback_query

    # Get rejected users
    rejected_users = get_rejected_users()

    if not rejected_users:
        await query.edit_message_text(
            text="There are no rejected users."
        )
        return

    # Create keyboard with user buttons
    keyboard = []
    for user_id, user_data in rejected_users.items():
        user_name = user_data.get('first_name', 'Unknown')
        username = user_data.get('username', '')
        display_name = f"{user_name} (@{username})" if username else user_name
        keyboard.append([InlineKeyboardButton(display_name, callback_data=f"user_rejected_{user_id}")])

    # Add back button
    keyboard.append([InlineKeyboardButton("◀️ Back", callback_data="back_to_user_menu")])

    reply_markup = InlineKeyboardMarkup(keyboard)

    await query.edit_message_text(
        text="Select a rejected user to manage:",
        reply_markup=reply_markup
    )

async def handle_user_approved_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
    """Handle selection of an approved user."""
    query = update.callback_query
    user_id = callback_data.split("_")[2]

    # Get user data
    user_data = get_user_data(int(user_id))

    if not user_data:
        await query.edit_message_text(
            text=f"User {user_id} not found."
        )
        return

    # Format user info
    user_name = user_data.get('first_name', 'Unknown')
    last_name = user_data.get('last_name', '')
    username = user_data.get('username', '')

    user_info = (
        f"<b>User Information:</b>\n"
        f"ID: {user_id}\n"
        f"Name: {user_name} {last_name}\n"
        f"Username: {('@' + username) if username else 'None'}"
    )

    # Create keyboard with options
    keyboard = [
        [InlineKeyboardButton("✉️ Send Message", callback_data=f"message_user_{user_id}")],
        [InlineKeyboardButton("🚫 Move to Rejected", callback_data=f"move_to_rejected_{user_id}")],
        [InlineKeyboardButton("◀️ Back to Users", callback_data="list_approved_users")]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    await query.edit_message_text(
        text=user_info,
        reply_markup=reply_markup,
        parse_mode='HTML'
    )

async def handle_user_rejected_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
    """Handle selection of a rejected user."""
    query = update.callback_query
    user_id = callback_data.split("_")[2]

    # Get user data
    rejected_users = get_rejected_users()
    user_data = rejected_users.get(user_id, {})

    if not user_data:
        await query.edit_message_text(
            text=f"User {user_id} not found."
        )
        return

    # Format user info
    user_name = user_data.get('first_name', 'Unknown')
    last_name = user_data.get('last_name', '')
    username = user_data.get('username', '')

    user_info = (
        f"<b>User Information:</b>\n"
        f"ID: {user_id}\n"
        f"Name: {user_name} {last_name}\n"
        f"Username: {('@' + username) if username else 'None'}\n"
        f"Status: <b>Rejected</b>"
    )

    # Create keyboard with options
    keyboard = [
        [InlineKeyboardButton("✉️ Send Message", callback_data=f"message_user_{user_id}")],
        [InlineKeyboardButton("✅ Move to Approved", callback_data=f"move_to_approved_{user_id}")],
        [InlineKeyboardButton("◀️ Back to Users", callback_data="list_rejected_users")]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    await query.edit_message_text(
        text=user_info,
        reply_markup=reply_markup,
        parse_mode='HTML'
    )

async def handle_message_user_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
    """Handle sending a message to a user."""
    query = update.callback_query
    user_id = callback_data.split("_")[2]

    # Store the user ID in context for the next step
    context.user_data['message_to_user_id'] = user_id

    await query.edit_message_text(
        text=f"Please send the message you want to send to user {user_id}.\n\n"
             f"Type /cancel to cancel."
    )

    # Set conversation state
    context.user_data['waiting_for_message'] = True

async def handle_move_to_rejected_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
    """Handle moving a user from approved to rejected."""
    query = update.callback_query
    user_id = int(callback_data.split("_")[3])

    # Move user to rejected
    reject_user(user_id)

    await query.edit_message_text(
        text=f"User {user_id} has been moved to the rejected list."
    )

    # Notify user
    try:
        await context.bot.send_message(
            chat_id=user_id,
            text=(
                "We're sorry, but your access has been revoked.\n\n"
                f"If you believe this is an error, please contact {OWNER_USER_NAME} for assistance."
            )
        )
    except Exception as e:
        logger.error(f"Failed to notify user {user_id}: {e}")

async def handle_move_to_approved_callback(update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
    """Handle moving a user from rejected to approved."""
    query = update.callback_query
    user_id = int(callback_data.split("_")[3])

    # Get user data from rejected users
    rejected_users = get_rejected_users()
    user_data = rejected_users.get(str(user_id), {'id': user_id})

    # Move user to approved
    approve_user(user_data)

    await query.edit_message_text(
        text=f"User {user_id} has been moved to the approved list."
    )

    # Notify user
    try:
        await context.bot.send_message(
            chat_id=user_id,
            text=(
                "🎉 Congratulations! Your access has been approved.\n\n"
                "You can now receive trading signals. Make sure to turn on notifications "
                "to get alerts for new signals.\n\n"
                "Type /help to learn how to use the signals effectively."
            )
        )
    except Exception as e:
        logger.error(f"Failed to notify user {user_id}: {e}")

async def handle_trade_spot_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle request for a spot trade."""
    query = update.callback_query
    user_id = update.effective_user.id

    # Send a message that we're looking for a high-quality spot trade
    await query.edit_message_text(
        text="🔍 *Searching for a high-quality SPOT trade...*\n\n"
             "This may take a few minutes as we analyze the market for the best opportunities.\n\n"
             "You will be notified when a trade is found that meets our quality criteria.",
        parse_mode='Markdown'
    )

    # Create a unique command ID
    command_id = str(uuid.uuid4())

    # Add command to shared data to trigger trade search
    add_command({
        "id": command_id,
        "type": "find_trade",
        "market_type": "SPOT",
        "user_id": user_id,
        "timestamp": datetime.now().isoformat()
    })

    # Log the request
    logger.info(f"User {user_id} requested a spot trade")

async def handle_trade_futures_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle request for a futures trade."""
    query = update.callback_query
    user_id = update.effective_user.id

    # Send a message that we're looking for a high-quality futures trade
    await query.edit_message_text(
        text="🔍 *Searching for a high-quality FUTURES trade...*\n\n"
             "This may take a few minutes as we analyze the market for the best opportunities.\n\n"
             "You will be notified when a trade is found that meets our quality criteria.",
        parse_mode='Markdown'
    )

    # Create a unique command ID
    command_id = str(uuid.uuid4())

    # Add command to shared data to trigger trade search
    add_command({
        "id": command_id,
        "type": "find_trade",
        "market_type": "FUTURES",
        "user_id": user_id,
        "timestamp": datetime.now().isoformat()
    })

    # Log the request
    logger.info(f"User {user_id} requested a futures trade")

async def handle_wait_better_trade_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle user choosing to wait for a better trade."""
    query = update.callback_query
    user_id = update.effective_user.id

    # Remove the inline keyboard
    await query.edit_message_reply_markup(reply_markup=None)

    # Send a message with trading advice
    await query.edit_message_text(
        text="✅ *WISE CHOICE - WAITING FOR BETTER CONDITIONS*\n\n"
             "You've chosen to wait for a higher-quality trade. This is often the right decision!\n\n"
             "*Trading Wisdom:*\n"
             "• Professional traders are patient and selective\n"
             "• Waiting for ideal setups improves your win rate\n"
             "• Capital preservation is your first priority\n"
             "• The market will always provide new opportunities\n\n"
             "Use the /trade command again later when market conditions may have improved.",
        parse_mode='Markdown'
    )

    # Log the decision
    logger.info(f"User {user_id} chose to wait for a better trade")