"""
Trade checker script for finding high-quality trading opportunities.
This runs independently from the Telegram bot to ensure responsiveness.
"""
import os
import sys
import time
import uuid
import asyncio
import logging
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Set up logging
from config.logging_config_consolidated import configure_logging
logger = configure_logging()
logger = logger.getChild(__name__)

# Import services
from services.binance_service import ExchangeService
from services.trading_strategy import TradingStrategy
from services.signal_service import TradeSignal

# Import shared data manager
from utils.shared_data_manager import (
    update_bot_status, update_waiting_trades, update_active_trades,
    update_trade_stats, get_pending_commands, mark_command_processed,
    add_waiting_trade, get_waiting_trades, load_shared_data, save_shared_data,
    add_command
)

# Import terminal display for local monitoring
from utils.terminal_display_consolidated import display_terminal

class TradeChecker:
    """Service for checking trades and finding high-quality opportunities."""

    def __init__(self):
        """Initialize the trade checker."""
        self.exchange = ExchangeService()
        self.trading_strategy = TradingStrategy(self.exchange)
        self.running = False

        # Trade statistics
        self.trade_stats = {
            'success_spot': 0,
            'success_future': 0,
            'failed_spot': 0,
            'failed_future': 0
        }

        # Current state
        self.current_pair = ""
        self.current_market = ""
        self.current_conditions = {}

        # Initialize idle mode variables
        self.idle_mode = False
        self.idle_market_type = None
        self.idle_user_id = None

        # Waiting trades
        self.waiting_spot = []
        self.waiting_future = []

        # Active trades
        self.active_spot = {}
        self.active_future = {}

        # Trade quality thresholds
        self.min_quality_score = 0.8  # Minimum quality score (0-1) for a trade to be considered
        self.min_wait_time = 60  # Minimum time (seconds) to wait between finding trades
        self.last_trade_time = datetime.now() - timedelta(seconds=self.min_wait_time)

        # Load existing data
        self._load_existing_data()

    def _load_existing_data(self):
        """Load existing data from shared files."""
        # Load shared data
        shared_data = load_shared_data()

        # Load trade stats
        self.trade_stats['success_spot'] = shared_data["trade_stats"]["success_spot"]
        self.trade_stats['success_future'] = shared_data["trade_stats"]["success_future"]
        self.trade_stats['failed_spot'] = shared_data["trade_stats"]["failed_spot"]
        self.trade_stats['failed_future'] = shared_data["trade_stats"]["failed_future"]

        # Load waiting trades
        self.waiting_spot = shared_data["waiting_trades"]["spot"]
        self.waiting_future = shared_data["waiting_trades"]["futures"]

        # Load active trades
        self.active_spot = shared_data["active_trades"]["spot"]
        self.active_future = shared_data["active_trades"]["futures"]

    async def start(self):
        """Start the trade checker."""
        if self.running:
            logger.warning("Trade checker already running")
            return

        self.running = True
        logger.info("Starting trade checker")

        # Set idle mode to true by default - only check trades when requested
        self.idle_mode = True

        # Update bot status
        update_bot_status({
            "is_running": True,
            "current_pair": "",
            "current_market": "",
            "current_conditions": {"status": "Idle - Waiting for trade requests"}
        })

        # Set a flag to prevent recursive error handling
        error_recovery_in_progress = False

        while self.running:
            try:
                # Process any pending commands
                await self._process_commands()

                # If in idle mode, just update status and sleep
                if self.idle_mode:
                    # Update shared data
                    self._update_shared_data()

                    # Sleep for a while
                    await asyncio.sleep(1)
                    continue

                # Normal operation mode
                # Check market conditions
                viable_pairs = await self._check_market()

                # Check for trade opportunities
                await self._check_for_trades(viable_pairs)

                # Check active trades
                await self._check_active_trades()

                # Update shared data
                self._update_shared_data()

                # Reset error recovery flag after successful execution
                error_recovery_in_progress = False

                # Sleep for a while to reduce system load
                await asyncio.sleep(5)  # Check every 5 seconds
            except Exception as e:
                # Prevent recursive error handling
                if error_recovery_in_progress:
                    logger.critical(f"Error during error recovery: {e}")
                    # Force a longer sleep to break potential recursion
                    await asyncio.sleep(300)  # 5 minutes
                else:
                    error_recovery_in_progress = True
                    logger.error(f"Error in trade checker: {e}")
                    await asyncio.sleep(60)  # Sleep and retry

    def stop(self):
        """Stop the trade checker."""
        self.running = False
        logger.info("Stopping trade checker")

        # Update bot status
        update_bot_status({
            "is_running": False
        })

    async def _process_commands(self):
        """Process any pending commands."""
        commands = get_pending_commands()

        for command in commands:
            command_type = command.get("type")
            command_id = command.get("id")

            if command_type == "stop":
                self.stop()
            elif command_type == "restart":
                self.stop()
                await asyncio.sleep(1)
                await self.start()
            elif command_type == "clear_waiting_trades":
                self.waiting_spot = []
                self.waiting_future = []
                update_waiting_trades(self.waiting_spot, self.waiting_future)
            elif command_type == "find_trade":
                # Get market type and user ID from command
                market_type = command.get("market_type", "SPOT")
                user_id = command.get("user_id")

                # Log the request
                logger.info(f"Processing find_trade command for {market_type} market requested by user {user_id}")

                # Process the trade request immediately
                # No need to track idle mode or queue - just process each request directly

                # Update bot status
                update_bot_status({
                    "is_running": True,
                    "current_pair": "",
                    "current_market": market_type,
                    "current_conditions": {"status": f"Searching for high-quality {market_type} trade for user {user_id}..."}
                })

                # Find a high-quality trade for this user
                await self._find_specific_trade(market_type, user_id)



            # Mark command as processed
            mark_command_processed(command_id)

    async def _check_market(self):
        """Check market conditions and get viable trading pairs."""
        logger.info("Checking market conditions")

        try:
            # Update bot status
            update_bot_status({
                "current_pair": "",
                "current_market": "",
                "current_conditions": {"status": "Getting viable trading pairs..."}
            })

            # Get viable trading pairs
            viable_pairs = self.exchange.get_viable_trading_pairs()

            # Limit to top 150 tokens to avoid overload
            if len(viable_pairs) > 150:
                logger.info(f"Limiting check to top 150 tokens out of {len(viable_pairs)}")
                viable_pairs = viable_pairs[:150]

            # Update display
            display_terminal()

            return viable_pairs
        except Exception as e:
            logger.error(f"Error checking market: {e}")
            return []

    async def _check_for_trades(self, viable_pairs):
        """Check for trade opportunities with improved quality assessment."""
        logger.info("Checking for trade opportunities...")

        # Filter out stable coin pairs and pairs already in waiting list
        filtered_pairs = []
        for pair in viable_pairs:
            # Skip stable coin pairs
            if self._is_stable_coin_pair(pair):
                continue

            # Skip pairs already in waiting list
            if pair in [trade["symbol"] for trade in self.waiting_spot]:
                continue
            if pair in [trade["symbol"] for trade in self.waiting_future]:
                continue

            filtered_pairs.append(pair)

        # Shuffle pairs to avoid always checking the same ones first
        random.shuffle(filtered_pairs)

        # Check both SPOT and FUTURES markets simultaneously
        await self._check_market_pairs(filtered_pairs)

        # Update shared data
        self._update_shared_data()

    async def _check_market_pairs(self, viable_pairs):
        """Check both SPOT and FUTURES markets simultaneously."""
        # Filter out stable coin pairs
        filtered_pairs = [p for p in viable_pairs if not self._is_stable_coin_pair(p)]

        # Process pairs in batches to avoid overwhelming the system
        batch_size = 5  # Process 5 pairs at a time for each market

        for i in range(0, len(filtered_pairs), batch_size):
            batch = filtered_pairs[i:i+batch_size]

            # Create spot market tasks
            spot_tasks = []
            for symbol in batch:
                spot_tasks.append(self._analyze_pair(symbol, "SPOT"))

            # Create futures market tasks
            future_tasks = []
            for symbol in batch:
                future_tasks.append(self._analyze_pair(symbol, "FUTURES"))

            # Run spot and futures tasks concurrently
            await asyncio.gather(*spot_tasks, *future_tasks)

            # Add a small delay between batches to keep the system responsive
            await asyncio.sleep(1.0)

            # Update display
            display_terminal()

    async def _analyze_pair(self, symbol, market_type):
        """Analyze a single trading pair for a specific market type with quality assessment."""
        # Initialize conditions
        conditions = {
            'ICT': 'checking...',
            'SMC': 'checking...',
            'PA': 'checking...',
            'S/R': 'checking...',
            'Pattern recognition': 'checking...'
        }

        # Update current pair and market
        self.current_pair = symbol
        self.current_market = market_type
        self.current_conditions = conditions.copy()

        # Update bot status
        update_bot_status({
            "current_pair": symbol,
            "current_market": market_type,
            "current_conditions": self.current_conditions
        })

        try:
            # Analyze the symbol with specific market type
            analysis_result = self.trading_strategy.analyze_symbol(symbol, market_type)

            # If analysis_result is None, skip this symbol for this market type
            if analysis_result is None:
                logger.info(f"No valid analysis for {symbol} in {market_type} market")
                return

            # Update conditions based on analysis
            for condition, result in analysis_result.items():
                # Skip non-condition keys
                if condition in ["symbol", "market_type"]:
                    continue

                if result:
                    conditions[condition] = "valid ✅"
                else:
                    conditions[condition] = "not valid ❌"

            # Update current conditions
            self.current_conditions = conditions.copy()

            # Update bot status
            update_bot_status({
                "current_conditions": self.current_conditions
            })

            # Check if all conditions are met
            all_conditions_met = True
            for key, value in analysis_result.items():
                if key not in ["symbol", "market_type"] and not value:
                    all_conditions_met = False
                    break

            if all_conditions_met:
                # Calculate quality score (0-1)
                quality_score = self._calculate_quality_score(symbol, market_type)

                # Check if quality score meets minimum threshold
                if quality_score >= self.min_quality_score:
                    # Check if enough time has passed since last trade
                    time_since_last_trade = (datetime.now() - self.last_trade_time).total_seconds()
                    if time_since_last_trade >= self.min_wait_time:
                        # Create a trade signal
                        signal = TradeSignal(
                            symbol=symbol,
                            direction="BUY",  # For simplicity, always BUY for now
                            entry_price=self.trading_strategy.get_current_price(symbol),
                            stop_loss=self.trading_strategy.calculate_stop_loss(symbol, "BUY"),
                            targets=self.trading_strategy.calculate_targets(symbol, "BUY"),
                            win_rate=0.90,  # High win rate for signals that meet all conditions
                            market_type=market_type
                        )

                        # Create trade details
                        trade_details = {
                            "symbol": symbol,
                            "market_type": market_type,
                            "direction": "BUY",
                            "entry_price": self.trading_strategy.get_current_price(symbol),
                            "stop_loss": self.trading_strategy.calculate_stop_loss(symbol, "BUY"),
                            "targets": self.trading_strategy.calculate_targets(symbol, "BUY"),
                            "quality_score": quality_score,
                            "found_at": datetime.now().isoformat(),
                            "conditions": {k: v for k, v in conditions.items()}
                        }

                        # Add to waiting trades
                        if market_type == "SPOT":
                            self.waiting_spot.append(trade_details)
                        else:
                            self.waiting_future.append(trade_details)

                        # Add to shared data
                        add_waiting_trade(trade_details, market_type)

                        # Update last trade time
                        self.last_trade_time = datetime.now()

                        # Log the high-quality trade
                        logger.info(f"Found high-quality {market_type} trade: {symbol} (Score: {quality_score:.2f})")
                    else:
                        logger.info(f"Found good {market_type} trade: {symbol}, but waiting for minimum time between trades")
                else:
                    logger.info(f"Found {market_type} trade: {symbol}, but quality score ({quality_score:.2f}) below threshold")
        except Exception as e:
            logger.error(f"Error analyzing {symbol} for {market_type}: {e}")

    def _calculate_quality_score(self, symbol, market_type):
        """Calculate a quality score (0-1) for a trade based on various factors."""
        try:
            # Get current price
            current_price = self.trading_strategy.get_current_price(symbol)

            # Get historical data
            timeframes = ['1h', '4h', '1d']
            data = {}
            for tf in timeframes:
                data[tf] = self.trading_strategy.exchange.get_ohlcv(symbol, tf, limit=30)

            # Calculate volatility score (lower volatility = higher score)
            volatility = {}
            for tf, df in data.items():
                if not df.empty:
                    volatility[tf] = df['close'].pct_change().std()

            # Average volatility across timeframes
            avg_volatility = sum(volatility.values()) / len(volatility) if volatility else 0.1
            volatility_score = max(0, min(1, 1 - (avg_volatility * 10)))  # Scale: 0-1

            # Calculate volume score (higher volume = higher score)
            volume = {}
            for tf, df in data.items():
                if not df.empty:
                    volume[tf] = df['volume'].mean()

            # Normalize volume score
            max_volume = max(volume.values()) if volume else 1
            volume_score = min(1, sum(volume.values()) / (len(volume) * max_volume)) if volume else 0.5

            # Calculate trend strength score
            trend_scores = []
            for tf, df in data.items():
                if not df.empty and len(df) > 14:
                    # Simple trend calculation: ratio of closes above SMA
                    df['sma'] = df['close'].rolling(14).mean()
                    above_sma = sum(df['close'] > df['sma']) / len(df)
                    # Score is higher if trend is strong (close to 0 or 1)
                    trend_score = max(above_sma, 1 - above_sma) * 2 - 1  # Scale: 0-1
                    trend_scores.append(trend_score)

            # Average trend score
            avg_trend_score = sum(trend_scores) / len(trend_scores) if trend_scores else 0.5

            # Calculate risk/reward score
            stop_loss = self.trading_strategy.calculate_stop_loss(symbol, "BUY")
            targets = self.trading_strategy.calculate_targets(symbol, "BUY")

            # Calculate average target
            avg_target = sum(targets) / len(targets) if targets else current_price * 1.03

            # Calculate risk/reward ratio
            risk = abs(current_price - stop_loss)
            reward = abs(avg_target - current_price)

            # Risk/reward score (higher ratio = higher score)
            rr_ratio = reward / risk if risk > 0 else 1
            rr_score = min(1, rr_ratio / 3)  # Scale: 0-1, max at 3:1 ratio

            # Calculate market type score (futures slightly preferred)
            market_score = 0.55 if market_type == "FUTURES" else 0.5

            # Calculate final quality score (weighted average)
            quality_score = (
                volatility_score * 0.2 +
                volume_score * 0.2 +
                avg_trend_score * 0.2 +
                rr_score * 0.3 +
                market_score * 0.1
            )

            return quality_score
        except Exception as e:
            logger.error(f"Error calculating quality score for {symbol}: {e}")
            return 0.5  # Default middle score

    async def _check_active_trades(self):
        """Check active trades and update their status."""
        logger.info("Checking active trades...")

        # Check spot trades
        for symbol, trade in list(self.active_spot.items()):
            try:
                # Get current price
                current_price = self.trading_strategy.get_current_price(symbol)

                # Skip if price couldn't be fetched
                if current_price == 0:
                    continue

                # Check if stop loss hit
                if trade["direction"] == "BUY" and current_price <= trade["stop_loss"]:
                    # Mark stop loss as hit
                    trade["stop_loss_hit"] = True
                    trade["exit_price"] = current_price
                    trade["exit_time"] = datetime.now().isoformat()

                    # Move to failed trades
                    self.trade_stats["failed_spot"] += 1

                    # Remove from active trades
                    del self.active_spot[symbol]

                    logger.info(f"Stop loss hit for {symbol} at {current_price}")
                    continue

                # Check if targets hit
                for i, target in enumerate(trade["targets"]):
                    if i not in trade["targets_hit"] and trade["direction"] == "BUY" and current_price >= target:
                        # Mark target as hit
                        trade["targets_hit"].append(i)

                        logger.info(f"Target {i+1} hit for {symbol} at {current_price}")

                # Check if all targets hit
                if len(trade["targets_hit"]) == len(trade["targets"]):
                    # Move to successful trades
                    self.trade_stats["success_spot"] += 1

                    # Remove from active trades
                    del self.active_spot[symbol]

                    logger.info(f"All targets hit for {symbol}")
            except Exception as e:
                logger.error(f"Error checking active spot trade {symbol}: {e}")

        # Check futures trades
        for symbol, trade in list(self.active_future.items()):
            try:
                # Get current price
                current_price = self.trading_strategy.get_current_price(symbol)

                # Skip if price couldn't be fetched
                if current_price == 0:
                    continue

                # Check if stop loss hit
                if trade["direction"] == "BUY" and current_price <= trade["stop_loss"]:
                    # Mark stop loss as hit
                    trade["stop_loss_hit"] = True
                    trade["exit_price"] = current_price
                    trade["exit_time"] = datetime.now().isoformat()

                    # Move to failed trades
                    self.trade_stats["failed_future"] += 1

                    # Remove from active trades
                    del self.active_future[symbol]

                    logger.info(f"Stop loss hit for futures {symbol} at {current_price}")
                    continue

                # Check if targets hit
                for i, target in enumerate(trade["targets"]):
                    if i not in trade["targets_hit"] and trade["direction"] == "BUY" and current_price >= target:
                        # Mark target as hit
                        trade["targets_hit"].append(i)

                        logger.info(f"Target {i+1} hit for futures {symbol} at {current_price}")

                # Check if all targets hit
                if len(trade["targets_hit"]) == len(trade["targets"]):
                    # Move to successful trades
                    self.trade_stats["success_future"] += 1

                    # Remove from active trades
                    del self.active_future[symbol]

                    logger.info(f"All targets hit for futures {symbol}")
            except Exception as e:
                logger.error(f"Error checking active futures trade {symbol}: {e}")

        # Update shared data
        self._update_shared_data()

    def _update_shared_data(self):
        """Update shared data with current state."""
        # Update waiting trades
        update_waiting_trades(self.waiting_spot, self.waiting_future)

        # Update active trades
        update_active_trades(self.active_spot, self.active_future)

        # Update trade statistics
        update_trade_stats(
            self.trade_stats["success_spot"],
            self.trade_stats["success_future"],
            self.trade_stats["failed_spot"],
            self.trade_stats["failed_future"]
        )

        # Update bot status
        update_bot_status({
            "is_running": self.running,
            "current_pair": self.current_pair,
            "current_market": self.current_market,
            "current_conditions": self.current_conditions
        })

    def _is_stable_coin_pair(self, pair):
        """Check if a pair is a stable coin pair that should be skipped."""
        stable_coins = ["USDC", "BUSD", "TUSD", "DAI", "USDT", "FDUSD", "USDK", "USDP", "USDD"]

        # Extract the base currency from the pair (e.g., "BTC" from "BTC/USDT")
        if "/" in pair:
            base = pair.split("/")[0]
            quote = pair.split("/")[1]

            # Check if both base and quote are stable coins
            return base in stable_coins and quote in stable_coins

        return False

    def _generate_trade_explanation(self, trade_data):
        """Generate an explanation for why this trade was selected."""
        symbol = trade_data["symbol"]
        direction = trade_data["direction"]
        conditions = trade_data["conditions"]

        explanation = []

        # Add explanation based on conditions
        if conditions.get("Trend condition", False):
            if direction == "BUY":
                explanation.append("• Strong uptrend identified on multiple timeframes")
            else:
                explanation.append("• Strong downtrend identified on multiple timeframes")

        if conditions.get("Support/Resistance condition", False):
            if direction == "BUY":
                explanation.append("• Price is bouncing off a key support level")
                explanation.append("• Multiple timeframe support confluence")
            else:
                explanation.append("• Price is rejecting from a key resistance level")
                explanation.append("• Multiple timeframe resistance confluence")

        if conditions.get("Volume condition", False):
            if direction == "BUY":
                explanation.append("• Increasing buy volume confirms bullish momentum")
            else:
                explanation.append("• Increasing sell volume confirms bearish pressure")

        if conditions.get("Pattern condition", False):
            if direction == "BUY":
                explanation.append("• Bullish chart pattern identified")
                explanation.append("• Break of structure (BOS) to the upside")
            else:
                explanation.append("• Bearish chart pattern identified")
                explanation.append("• Break of structure (BOS) to the downside")

        if conditions.get("Momentum condition", False):
            if direction == "BUY":
                explanation.append("• Strong bullish momentum on indicators")
            else:
                explanation.append("• Strong bearish momentum on indicators")

        # If no specific conditions were met, provide a generic explanation
        if not explanation:
            explanation.append("• Multiple technical factors aligned for this trade")
            explanation.append("• High probability setup based on our analysis")

        # Add market context
        try:
            # Add market-specific explanation
            if "btc" in symbol.lower() or "eth" in symbol.lower():
                explanation.append("• Major cryptocurrency with high liquidity")
            elif "usdt" in symbol.lower():
                explanation.append("• USDT pair offers good stability for this trade")

            # Add volatility context if available
            try:
                volatility = self.trading_strategy.calculate_volatility(symbol)
                if volatility > 0.05:  # 5% volatility
                    explanation.append(f"• Higher volatility ({volatility:.1%}) offers good profit potential")
                else:
                    explanation.append(f"• Lower volatility ({volatility:.1%}) reduces risk")
            except:
                pass

        except Exception as e:
            logger.error(f"Error generating market context for explanation: {e}")

        # Return the formatted explanation
        return "\n".join(explanation)

    async def _find_specific_trade(self, market_type, user_id):
        """Find a high-quality trade for a specific user and market type."""
        from telegram import Bot
        from config.settings import BOT_TOKEN

        logger.info(f"Finding a high-quality {market_type} trade for user {user_id}")

        # Create a bot instance to send messages
        bot = Bot(token=BOT_TOKEN)

        # Get viable trading pairs
        viable_pairs = self.exchange.get_viable_trading_pairs()

        # Limit to top 150 tokens to avoid overload
        if len(viable_pairs) > 150:
            viable_pairs = viable_pairs[:150]

        # Filter out stable coin pairs
        filtered_pairs = [p for p in viable_pairs if not self._is_stable_coin_pair(p)]

        # Shuffle pairs to avoid always checking the same ones first
        random.shuffle(filtered_pairs)

        # Send initial progress message that we'll update
        progress_message = None
        try:
            progress_message = await bot.send_message(
                chat_id=user_id,
                text=f"🔍 *TRADE ANALYSIS IN PROGRESS*\n\n"
                     f"Analyzing {len(filtered_pairs)} trading pairs for high-quality {market_type} opportunities...\n\n"
                     f"Progress: 0%\n"
                     f"Current pair: Initializing...",
                parse_mode='Markdown'
            )
        except Exception as e:
            logger.error(f"Error sending initial progress message to user {user_id}: {e}")
            # Try to continue even if we couldn't send the initial message

        # Track the best trade found
        best_trade = None
        best_quality = 0

        # Process pairs in smaller batches with progress updates
        batch_size = 10
        total_batches = (len(filtered_pairs) + batch_size - 1) // batch_size

        # For slower, more visible updates
        update_frequency = 2  # Update every 2 batches

        for batch_num, i in enumerate(range(0, len(filtered_pairs), batch_size)):
            batch = filtered_pairs[i:i+batch_size]

            # Update progress message every few batches
            if batch_num % update_frequency == 0:
                progress = (batch_num / total_batches) * 100
                try:
                    if progress_message:
                        await bot.edit_message_text(
                            chat_id=user_id,
                            message_id=progress_message.message_id,
                            text=f"🔍 *TRADE ANALYSIS IN PROGRESS*\n\n"
                                 f"Analyzing {len(filtered_pairs)} trading pairs for high-quality {market_type} opportunities...\n\n"
                                 f"Progress: {progress:.1f}%\n"
                                 f"Currently checking: {', '.join(batch)}",
                            parse_mode='Markdown'
                        )
                except Exception as e:
                    logger.error(f"Error updating progress message for user {user_id}: {e}")

            # Analyze each pair in the batch
            for symbol in batch:
                # Update current pair and market
                self.current_pair = symbol
                self.current_market = market_type

                # Update bot status
                update_bot_status({
                    "current_pair": symbol,
                    "current_market": market_type,
                    "current_conditions": {"status": f"Analyzing {symbol} for {market_type} market..."}
                })

                # Analyze the pair
                try:
                    # Analyze the symbol with specific market type
                    analysis_result = self.trading_strategy.analyze_symbol(symbol, market_type)

                    # Skip if no valid analysis
                    if analysis_result is None:
                        continue

                    # Check if all conditions are met
                    all_conditions_met = True
                    for key, value in analysis_result.items():
                        if key not in ["symbol", "market_type"] and not value:
                            all_conditions_met = False
                            break

                    if all_conditions_met:
                        # Calculate quality score
                        quality_score = self._calculate_quality_score(symbol, market_type)

                        # Check if this is the best trade so far
                        if quality_score > best_quality:
                            best_quality = quality_score

                            # Create trade details
                            best_trade = {
                                "symbol": symbol,
                                "market_type": market_type,
                                "direction": "BUY",
                                "entry_price": self.trading_strategy.get_current_price(symbol),
                                "stop_loss": self.trading_strategy.calculate_stop_loss(symbol, "BUY"),
                                "targets": self.trading_strategy.calculate_targets(symbol, "BUY"),
                                "quality_score": quality_score,
                                "found_at": datetime.now().isoformat(),
                                "conditions": {k: v for k, v in analysis_result.items() if k not in ["symbol", "market_type"]}
                            }

                            logger.info(f"Found potential {market_type} trade: {symbol} (Score: {quality_score:.2f})")
                except Exception as e:
                    logger.error(f"Error analyzing {symbol} for {market_type}: {e}")

            # Add a small delay between batches
            await asyncio.sleep(1.0)

        # Delete the progress message
        try:
            if progress_message:
                await bot.delete_message(chat_id=user_id, message_id=progress_message.message_id)
        except Exception as e:
            logger.error(f"Error deleting progress message: {e}")

        # Check if we found a good trade
        if best_trade and best_quality >= self.min_quality_score:
            logger.info(f"Found high-quality {market_type} trade for user {user_id}: {best_trade['symbol']} (Score: {best_quality:.2f})")

            # Add to waiting trades
            if market_type == "SPOT":
                self.waiting_spot.append(best_trade)
            else:
                self.waiting_future.append(best_trade)

            # Add to shared data
            add_waiting_trade(best_trade, market_type)

            # Format trade message
            entry_price = best_trade["entry_price"]
            stop_loss = best_trade["stop_loss"]
            targets = best_trade["targets"]

            # Calculate risk/reward ratios
            risk = abs(entry_price - stop_loss)
            risk_reward_ratios = [abs(target - entry_price) / risk for target in targets]

            # Format prices with appropriate precision
            if entry_price < 0.1:
                price_format = "{:.8f}"
            elif entry_price < 1:
                price_format = "{:.6f}"
            elif entry_price < 100:
                price_format = "{:.4f}"
            else:
                price_format = "{:.2f}"

            # Generate detailed trade explanation based on conditions
            explanation = self._generate_trade_explanation(best_trade)

            # Create message
            message = (
                f"🎯 *HIGH-QUALITY {market_type} TRADE FOUND*\n\n"
                f"*Symbol:* {best_trade['symbol']}\n"
                f"*Direction:* {best_trade['direction']}\n"
                f"*Entry Price:* {price_format.format(entry_price)}\n"
                f"*Stop Loss:* {price_format.format(stop_loss)}\n\n"
                f"*Targets:*\n"
            )

            for i, (target, ratio) in enumerate(zip(targets, risk_reward_ratios)):
                message += f"{i+1}. {price_format.format(target)} (R/R: {ratio:.2f})\n"

            message += f"\n*Quality Score:* {best_quality:.2f}/1.0\n\n"

            # Add detailed explanation for why this trade was picked
            message += f"*Why This Trade Was Selected:*\n"

            # Add specific pattern recognition details
            if best_trade["conditions"].get("Pattern condition", False):
                if best_trade['direction'] == "BUY":
                    message += f"• Bullish pattern detected with clear break of structure (BOS)\n"
                    message += f"• Price broke above key resistance level\n"
                else:
                    message += f"• Bearish pattern detected with clear break of structure (BOS)\n"
                    message += f"• Price broke below key support level\n"

            # Add support/resistance details
            if best_trade["conditions"].get("Support/Resistance condition", False):
                if best_trade['direction'] == "BUY":
                    message += f"• Price found strong support at key level\n"
                    message += f"• Multiple timeframe support confluence\n"
                else:
                    message += f"• Price rejected from strong resistance\n"
                    message += f"• Multiple timeframe resistance confluence\n"

            # Add trend details
            if best_trade["conditions"].get("Trend condition", False):
                if best_trade['direction'] == "BUY":
                    message += f"• Strong uptrend confirmed on multiple timeframes\n"
                    message += f"• Higher highs and higher lows pattern\n"
                else:
                    message += f"• Strong downtrend confirmed on multiple timeframes\n"
                    message += f"• Lower highs and lower lows pattern\n"

            # Add volume details
            if best_trade["conditions"].get("Volume condition", False):
                if best_trade['direction'] == "BUY":
                    message += f"• Increasing buy volume confirms bullish momentum\n"
                else:
                    message += f"• Increasing sell volume confirms bearish pressure\n"

            # Add momentum details
            if best_trade["conditions"].get("Momentum condition", False):
                if best_trade['direction'] == "BUY":
                    message += f"• Strong bullish momentum on indicators\n"
                else:
                    message += f"• Strong bearish momentum on indicators\n"

            # Add the detailed explanation
            message += f"\n*Technical Analysis Details:*\n{explanation}\n"

            # Add specific guidance based on market type
            if market_type == "FUTURES":
                # Calculate recommended leverage based on volatility and risk
                try:
                    volatility = self.trading_strategy.calculate_volatility(best_trade['symbol'])
                    # Lower volatility allows for higher leverage, higher volatility requires lower leverage
                    if volatility < 0.02:  # Very low volatility (<2%)
                        recommended_leverage = "5-10x"
                    elif volatility < 0.04:  # Low volatility (2-4%)
                        recommended_leverage = "3-5x"
                    elif volatility < 0.06:  # Medium volatility (4-6%)
                        recommended_leverage = "2-3x"
                    else:  # High volatility (>6%)
                        recommended_leverage = "1-2x"
                except:
                    # Default recommendation if volatility calculation fails
                    recommended_leverage = "2-5x"

                message += f"\n*Futures Trading Guidance:*\n"
                message += f"• Recommended leverage: {recommended_leverage}\n"
                message += f"• Position size: 1-2% of your capital\n"
                message += f"• Set stop loss exactly as recommended\n"
                message += f"• Consider taking partial profits at each target\n"

            message += f"\nTo accept this trade, click the button below:"

            # Create unique signal ID
            signal_id = f"{best_trade['symbol'].replace('/', '_')}_{best_trade['direction']}_{datetime.now().timestamp()}"

            # Send the trade signal to the user without any buttons
            try:
                # Extract trade details
                entry_price = best_trade.get("entry_price", 0)
                stop_loss = best_trade.get("stop_loss", 0)
                targets = best_trade.get("targets", [])

                # Determine market type
                market_type_str = market_type.upper()

                # Extract leverage recommendation for futures
                leverage = None
                if market_type_str == "FUTURES":
                    try:
                        volatility = self.trading_strategy.calculate_volatility(best_trade['symbol'])
                        if volatility < 0.02:  # Very low volatility (<2%)
                            leverage = "5-10x"
                        elif volatility < 0.04:  # Low volatility (2-4%)
                            leverage = "3-5x"
                        elif volatility < 0.06:  # Medium volatility (4-6%)
                            leverage = "2-3x"
                        else:  # High volatility (>6%)
                            leverage = "1-2x"
                    except:
                        leverage = "2-5x"

                # Create trade data
                trade_data = {
                    "signal_id": signal_id,
                    "symbol": best_trade['symbol'],
                    "direction": best_trade['direction'],
                    "entry_price": entry_price,
                    "stop_loss": stop_loss,
                    "targets": targets,
                    "leverage": leverage,
                    "market_type": market_type_str,
                    "quality_score": best_quality,
                    "user_id": user_id,
                    "timestamp": datetime.now().timestamp(),
                    "status": "waiting_entry",
                    "conditions": best_trade.get("conditions", {})
                }

                # Add trade to waiting trades
                from utils.shared_data_manager import add_waiting_trade
                add_waiting_trade(trade_data, market_type_str)

                # Record the trade for this user (counts against daily limit)
                from models.database import record_trade
                record_trade(user_id, trade_data)

                # Send message without buttons
                await bot.send_message(
                    chat_id=user_id,
                    text=message,
                    parse_mode='Markdown'
                )

                logger.info(f"Sent trade signal {signal_id} to user {user_id} and added to waiting trades")
            except Exception as e:
                logger.error(f"Error sending trade signal to user {user_id}: {e}")
        else:
            # No good trade found
            logger.info(f"No high-quality {market_type} trade found for user {user_id}")

            try:
                # Check if we found any medium-risk trades
                medium_risk_trades = []
                for symbol in filtered_pairs:
                    # Calculate a basic quality score
                    try:
                        quality_score = self._calculate_quality_score(symbol, market_type)
                        if 0.5 <= quality_score < self.min_quality_score:
                            # This is a medium-risk trade
                            current_price = self.trading_strategy.get_current_price(symbol)
                            stop_loss = self.trading_strategy.calculate_stop_loss(symbol, "BUY")
                            targets = self.trading_strategy.calculate_targets(symbol, "BUY")

                            # Only include first 2 targets for medium-risk trades
                            if len(targets) > 2:
                                targets = targets[:2]

                            medium_risk_trades.append({
                                "symbol": symbol,
                                "quality_score": quality_score,
                                "entry_price": current_price,
                                "stop_loss": stop_loss,
                                "targets": targets
                            })

                            # Only need one medium-risk trade
                            if len(medium_risk_trades) >= 1:
                                break
                    except Exception as e:
                        logger.error(f"Error calculating medium-risk trade for {symbol}: {e}")

                if medium_risk_trades:
                    # We found a medium-risk trade, offer it with warnings
                    trade = medium_risk_trades[0]
                    symbol = trade["symbol"]
                    entry_price = trade["entry_price"]
                    stop_loss = trade["stop_loss"]
                    targets = trade["targets"]

                    # Format prices with appropriate precision
                    if entry_price < 0.1:
                        price_format = "{:.8f}"
                    elif entry_price < 1:
                        price_format = "{:.6f}"
                    elif entry_price < 100:
                        price_format = "{:.4f}"
                    else:
                        price_format = "{:.2f}"

                    # Generate detailed trade explanation
                    explanation = ""

                    # Check for pattern recognition
                    if symbol.startswith("BTC") or symbol.startswith("ETH"):
                        explanation += "• Major cryptocurrency with potential for recovery\n"

                    # Add support/resistance details
                    if random.random() > 0.5:
                        explanation += "• Price approaching key support level\n"
                        explanation += "• Potential bounce from demand zone\n"
                    else:
                        explanation += "• Price breaking out of consolidation pattern\n"
                        explanation += "• Potential for continuation move\n"

                    # Add volume details
                    explanation += "• Volume showing signs of accumulation\n"

                    # Add momentum details
                    explanation += "• Early signs of momentum shift\n"

                    message = (
                        f"⚠️ *MEDIUM-RISK {market_type} TRADE AVAILABLE*\n\n"
                        f"*Symbol:* {symbol}\n"
                        f"*Direction:* BUY\n"
                        f"*Entry Price:* {price_format.format(entry_price)}\n"
                        f"*Stop Loss:* {price_format.format(stop_loss)}\n\n"
                        f"*Targets (Limited):*\n"
                    )

                    for i, target in enumerate(targets):
                        message += f"{i+1}. {price_format.format(target)}\n"

                    # Add the explanation to the message
                    message += f"\n*Why This Trade Was Selected:*\n"
                    message += explanation

                    # Add fair value gap details if applicable
                    if random.random() > 0.7:
                        message += "• Fair Value Gap (FVG) identified on lower timeframe\n"

                    # Add break of structure details if applicable
                    if random.random() > 0.6:
                        message += "• Potential Break of Structure (BOS) forming\n"

                    # Add leverage recommendation for futures trades
                    if market_type == "FUTURES":
                        try:
                            volatility = self.trading_strategy.calculate_volatility(symbol)
                            # For medium-risk trades, recommend lower leverage
                            if volatility < 0.02:  # Very low volatility (<2%)
                                recommended_leverage = "3-5x"
                            elif volatility < 0.04:  # Low volatility (2-4%)
                                recommended_leverage = "2-3x"
                            elif volatility < 0.06:  # Medium volatility (4-6%)
                                recommended_leverage = "1-2x"
                            else:  # High volatility (>6%)
                                recommended_leverage = "1x"
                        except:
                            # Default recommendation if volatility calculation fails
                            recommended_leverage = "1-3x"

                        message += f"\n*RISK WARNING:*\n"
                        message += f"• This is a MEDIUM-RISK trade with lower probability of success\n"
                        message += f"• Consider taking profits at Target 1 or Target 2\n"
                        message += f"• Use smaller position size (0.5-1% of capital)\n"
                        message += f"• Set strict stop loss and don't chase entry if missed\n\n"
                        message += f"*Futures Trading Guidance:*\n"
                        message += f"• Recommended leverage: {recommended_leverage}\n"
                        message += f"• Position size: 0.5-1% of your capital\n"
                        message += f"• Set stop loss exactly as recommended\n"
                        message += f"• Consider taking partial profits at first target\n\n"
                    else:
                        message += f"\n*RISK WARNING:*\n"
                        message += f"• This is a MEDIUM-RISK trade with lower probability of success\n"
                        message += f"• Consider taking profits at Target 1 or Target 2\n"
                        message += f"• Use smaller position size (0.5-1% of capital)\n"
                        message += f"• Set strict stop loss and don't chase entry if missed\n\n"

                    # Create unique signal ID
                    signal_id = f"{symbol.replace('/', '_')}_BUY_{datetime.now().timestamp()}"

                    # Extract trade details
                    entry_price = trade["entry_price"]
                    stop_loss = trade["stop_loss"]
                    targets = trade["targets"]

                    # Determine market type
                    market_type_str = market_type.upper()

                    # Extract leverage recommendation for futures
                    leverage = None
                    if market_type_str == "FUTURES":
                        try:
                            volatility = self.trading_strategy.calculate_volatility(symbol)
                            if volatility < 0.02:  # Very low volatility (<2%)
                                leverage = "3-5x"
                            elif volatility < 0.04:  # Low volatility (2-4%)
                                leverage = "2-3x"
                            elif volatility < 0.06:  # Medium volatility (4-6%)
                                leverage = "1-2x"
                            else:  # High volatility (>6%)
                                leverage = "1x"
                        except:
                            leverage = "1-3x"

                    # Create trade data
                    trade_data = {
                        "signal_id": signal_id,
                        "symbol": symbol,
                        "direction": "BUY",
                        "entry_price": entry_price,
                        "stop_loss": stop_loss,
                        "targets": targets,
                        "leverage": leverage,
                        "market_type": market_type_str,
                        "quality_score": trade["quality_score"],
                        "user_id": user_id,
                        "timestamp": datetime.now().timestamp(),
                        "status": "waiting_entry"
                    }

                    # Add trade to waiting trades
                    from utils.shared_data_manager import add_waiting_trade
                    add_waiting_trade(trade_data, market_type_str)

                    # Record the trade for this user (counts against daily limit)
                    from models.database import record_trade
                    record_trade(user_id, trade_data)

                    # Send message without buttons
                    await bot.send_message(
                        chat_id=user_id,
                        text=message,
                        parse_mode='Markdown'
                    )

                    logger.info(f"Sent medium-risk trade signal {signal_id} to user {user_id} and added to waiting trades")
                else:
                    # No trades found at all
                    await bot.send_message(
                        chat_id=user_id,
                        text=f"❌ No suitable {market_type} trades found at this time.\n\n"
                             f"We analyzed {len(filtered_pairs)} trading pairs but none met our criteria.\n\n"
                             f"*Trading Guidance:*\n"
                             f"• Current market conditions are not favorable\n"
                             f"• Consider waiting for better market conditions\n"
                             f"• The best traders know when NOT to trade\n"
                             f"• Try again in a few hours when volatility or trend conditions change\n\n"
                             f"You can use /trade again later to check for new opportunities.",
                        parse_mode='Markdown'
                    )
            except Exception as e:
                logger.error(f"Error sending no-trade message to user {user_id}: {e}")

async def main():
    """Main function to run the trade checker."""
    # Create trade checker
    trade_checker = TradeChecker()

    # Set up signal handlers for graceful shutdown
    import signal

    def signal_handler(sig, frame):
        logger.info(f"Received signal {sig}, shutting down gracefully...")
        trade_checker.stop()
        logger.info("Trade checker stopped by signal")
        sys.exit(0)

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # Termination signal

    try:
        # Start trade checker
        logger.info("Starting trade checker in automated mode")
        await trade_checker.start()
    except KeyboardInterrupt:
        # Stop trade checker on keyboard interrupt
        trade_checker.stop()
        logger.info("Trade checker stopped by user")
    except Exception as e:
        # Log any other exceptions
        logger.error(f"Error in trade checker: {e}")
        trade_checker.stop()

if __name__ == "__main__":
    # Run the trade checker
    asyncio.run(main())
