"""
User service for the trading bot.
"""
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

from models.database import (
    get_approved_users as get_approved_users_db,
    update_user_data, is_user_approved, is_user_rejected,
    approve_user as approve_user_db, reject_user as reject_user_db,
    get_user_trades, record_trade
)

logger = logging.getLogger(__name__)

# Maximum trades per day per user
MAX_TRADES_PER_DAY = 3

def get_approved_users() -> Dict[str, Dict[str, Any]]:
    """Get all approved users."""
    return get_approved_users_db()

def approve_user(user_data: Dict[str, Any]) -> bool:
    """Approve a user."""
    return approve_user_db(user_data)

def reject_user(user_id: int) -> bool:
    """Reject a user."""
    return reject_user_db(user_id)

def can_receive_trade(user_id: int) -> <PERSON><PERSON>[bool, str]:
    """Check if a user can receive a trade."""
    # Check if user is approved
    if not is_user_approved(user_id):
        return False, "User is not approved"
    
    # Check if user is rejected
    if is_user_rejected(user_id):
        return False, "User is rejected"
    
    # Get user's trades for today
    user_trades = get_user_trades(user_id)
    
    # Filter trades to only include those from today
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    today_trades = [
        trade for trade in user_trades
        if datetime.fromtimestamp(trade.get('timestamp', 0)) >= today
    ]
    
    # Check if user has reached their daily limit
    if len(today_trades) >= MAX_TRADES_PER_DAY:
        return False, f"User has reached their daily limit of {MAX_TRADES_PER_DAY} trades"
    
    return True, ""

def record_user_trade(user_id: int, signal_id: str) -> bool:
    """Record a trade for a user."""
    # Create trade record
    trade_data = {
        'signal_id': signal_id,
        'timestamp': datetime.now().timestamp(),
        'status': 'active'
    }
    
    # Record the trade
    return record_trade(user_id, trade_data)

def get_trades_reset_time() -> str:
    """Get the time when trades will reset."""
    tomorrow = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
    return tomorrow.strftime("%Y-%m-%d %H:%M:%S")
