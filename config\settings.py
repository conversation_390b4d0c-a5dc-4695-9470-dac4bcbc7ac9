import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Bot Configuration
BOT_TOKEN = os.getenv('BOT_TOKEN')
OWNER_ID = int(os.getenv('OWNER_ID'))
OWNER_USER_NAME = os.getenv('OWNER_USER_NAME')

# API Keys
CRYPTOCOMPARE_API_KEY = os.getenv('CRYPTOCOMPARE_API_KEY')
COINAPI_API_KEY = os.getenv('COINAPI_API_KEY')
BINANCE_API_KEY = os.getenv('BINANCE_API_KEY')
BINANCE_SECRET_KEY = os.getenv('BINANCE_SECRET_KEY')

# Database Files
APPROVED_USERS_FILE = "approved_users.json"
REJECTED_USERS_FILE = "rejected_users.json"
TRADE_HISTORY_FILE = "trade_history.json"

# Trade Settings
MAX_TRADES_PER_DAY = 3
MIN_WIN_RATE_PERCENT = 95
AVOID_SPOT_TOKENS_ABOVE_PRICE = 20  # in USD

# Trading Concepts to Check
TRADING_CONCEPTS = [
    "ICT",  # Institutional Candle Theory
    "SMC",  # Smart Money Concepts
    "PA",   # Price Action
    "S/R",  # Support/Resistance
    "Pattern Recognition"
]

# Background Task Settings
BACKGROUND_CHECK_INTERVAL = 300  # seconds (5 minutes)
