"""
Telegram bot server for the trading bot.
This runs independently from the trade checker to ensure responsiveness.
"""
import os
import sys
import time
import asyncio
import logging
import uuid
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import Telegram libraries
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application, CommandHandler, MessageHandler, CallbackQueryHandler,
    ContextTypes, filters
)

# Import configuration
from config.settings import (
    BOT_TOKEN, OWNER_ID, OWNER_USER_NAME, MAX_TRADES_PER_DAY
)

# Import command handlers
from handlers.command_handlers import (
    start_command, help_command, user_command, cancel_command
)

# Import callback handlers
from handlers.callback_handlers import handle_callback_query

# Import message handlers
from handlers.message_handlers import message_handler

# Import error handlers
from handlers.error_handlers import error_handler

# Import database functions
from models.database import (
    is_user_approved, can_receive_trade, ensure_files_exist
)

# Import shared data manager
from utils.shared_data_manager import (
    load_shared_data, save_shared_data, load_waiting_trades,
    load_active_trades, add_command, is_trade_checker_running,
    update_bot_status
)

# Import models
from models.database import ensure_files_exist

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def status_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show the current status of the trade checker."""
    # Check if user is authorized
    if str(update.effective_user.id) != str(OWNER_ID):
        await update.message.reply_text(
            "This command is only available to the bot owner."
        )
        return

    # Check if trade checker is running
    is_running = is_trade_checker_running()

    # Get current status
    status_data = load_shared_data().get("bot_status", {})
    current_pair = status_data.get("current_pair", "None")
    current_market = status_data.get("current_market", "None")
    current_conditions = status_data.get("current_conditions", {})

    # Format conditions
    conditions_text = ""
    for key, value in current_conditions.items():
        if key == "status":
            conditions_text += f"Status: {value}\n"
        else:
            conditions_text += f"{key}: {'✅' if value else '❌'}\n"

    # Send status message
    await update.message.reply_text(
        f"*Trade Checker Status*\n\n"
        f"Running: {'✅ Yes' if is_running else '❌ No'}\n"
        f"Current Pair: {current_pair}\n"
        f"Market Type: {current_market}\n\n"
        f"*Conditions:*\n{conditions_text}",
        parse_mode='Markdown'
    )

async def trades_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show the current waiting trades."""
    # Check if user is authorized
    if str(update.effective_user.id) != str(OWNER_ID):
        await update.message.reply_text(
            "This command is only available to the bot owner."
        )
        return

    # Get waiting trades
    spot_trades = load_waiting_trades("SPOT")
    future_trades = load_waiting_trades("FUTURES")

    # Format spot trades
    spot_trades_text = ""
    for trade in spot_trades:
        spot_trades_text += (
            f"Symbol: {trade['symbol']}\n"
            f"Direction: {trade['direction']}\n"
            f"Entry: {trade['entry_price']}\n"
            f"Stop Loss: {trade['stop_loss']}\n"
            f"Quality: {trade.get('quality_score', 'N/A')}\n\n"
        )

    # Format future trades
    future_trades_text = ""
    for trade in future_trades:
        future_trades_text += (
            f"Symbol: {trade['symbol']}\n"
            f"Direction: {trade['direction']}\n"
            f"Entry: {trade['entry_price']}\n"
            f"Stop Loss: {trade['stop_loss']}\n"
            f"Quality: {trade.get('quality_score', 'N/A')}\n\n"
        )

    # Send trades message
    await update.message.reply_text(
        f"*Waiting Trades*\n\n"
        f"*SPOT Trades ({len(spot_trades)}):*\n"
        f"{spot_trades_text or 'No waiting spot trades.'}\n\n"
        f"*FUTURES Trades ({len(future_trades)}):*\n"
        f"{future_trades_text or 'No waiting futures trades.'}",
        parse_mode='Markdown'
    )

async def start_checker_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Start the trade checker."""
    # Check if user is authorized
    if str(update.effective_user.id) != str(OWNER_ID):
        await update.message.reply_text(
            "This command is only available to the bot owner."
        )
        return

    # Check if trade checker is already running
    if is_trade_checker_running():
        await update.message.reply_text(
            "Trade checker is already running."
        )
        return

    try:
        # Update shared data to indicate trade checker should be running
        shared_data = load_shared_data()
        shared_data["trade_checker_running"] = True
        save_shared_data(shared_data)

        # Add command to start trade checker
        add_command({
            "id": str(uuid.uuid4()),
            "type": "start",
            "timestamp": datetime.now().isoformat()
        })

        # Start trade checker process
        process = await asyncio.create_subprocess_exec(
            sys.executable, "trade_checker.py",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        await update.message.reply_text(
            "Trade checker has been started."
        )
    except Exception as e:
        await update.message.reply_text(
            f"Error starting trade checker: {str(e)}"
        )

async def stop_checker_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Stop the trade checker."""
    # Check if user is authorized
    if str(update.effective_user.id) != str(OWNER_ID):
        await update.message.reply_text(
            "This command is only available to the bot owner."
        )
        return

    # Check if trade checker is running
    if not is_trade_checker_running():
        await update.message.reply_text(
            "Trade checker is not running."
        )
        return

    try:
        # Update shared data to indicate trade checker should stop
        shared_data = load_shared_data()
        shared_data["trade_checker_running"] = False
        save_shared_data(shared_data)

        # Add command to stop trade checker
        add_command({
            "id": str(uuid.uuid4()),
            "type": "stop",
            "timestamp": datetime.now().isoformat()
        })

        await update.message.reply_text(
            "Trade checker has been stopped."
        )
    except Exception as e:
        await update.message.reply_text(
            f"Error stopping trade checker: {str(e)}"
        )

async def restart_checker_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Restart the trade checker."""
    # Check if user is authorized
    if str(update.effective_user.id) != str(OWNER_ID):
        await update.message.reply_text(
            "This command is only available to the bot owner."
        )
        return

    try:
        # Update shared data to indicate trade checker should stop
        shared_data = load_shared_data()
        shared_data["trade_checker_running"] = False
        save_shared_data(shared_data)

        # Add command to stop trade checker
        add_command({
            "id": str(uuid.uuid4()),
            "type": "stop",
            "timestamp": datetime.now().isoformat()
        })

        # Wait a moment for the trade checker to stop
        await asyncio.sleep(2)

        # Check if trade checker is running
        if not is_trade_checker_running():
            # Start trade checker process
            process = await asyncio.create_subprocess_exec(
                sys.executable, "trade_checker.py",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            # Update shared data to indicate trade checker should be running
            shared_data = load_shared_data()
            shared_data["trade_checker_running"] = True
            save_shared_data(shared_data)

            # Add command to start trade checker
            add_command({
                "id": str(uuid.uuid4()),
                "type": "start",
                "timestamp": datetime.now().isoformat()
            })

        await update.message.reply_text(
            "Trade checker has been restarted."
        )
    except Exception as e:
        await update.message.reply_text(
            f"Error restarting trade checker: {str(e)}"
        )

async def clear_trades_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Clear all waiting trades."""
    # Check if user is authorized
    if str(update.effective_user.id) != str(OWNER_ID):
        await update.message.reply_text(
            "This command is only available to the bot owner."
        )
        return

    try:
        # Add command to clear trades
        add_command({
            "id": str(uuid.uuid4()),
            "type": "clear_trades",
            "timestamp": datetime.now().isoformat()
        })

        # Clear waiting trades in shared data
        shared_data = load_shared_data()
        shared_data["waiting_spot"] = []
        shared_data["waiting_future"] = []
        save_shared_data(shared_data)

        await update.message.reply_text(
            "All waiting trades have been cleared."
        )
    except Exception as e:
        await update.message.reply_text(
            f"Error clearing waiting trades: {str(e)}"
        )

async def trade_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle the /trade command to request a high-quality trade."""
    user_id = update.effective_user.id

    # Check if user is approved
    if not is_user_approved(user_id):
        await update.message.reply_text(
            "You need to be approved to access this command. "
            f"Please contact {OWNER_USER_NAME} for assistance."
        )
        return

    # Check if user has reached their daily trade limit
    can_receive, trades_remaining = can_receive_trade(user_id)
    if not can_receive:
        await update.message.reply_text(
            "❌ You have reached your daily trade limit.\n\n"
            f"You are limited to {MAX_TRADES_PER_DAY} trades per day. "
            f"Please try again tomorrow."
        )
        return

    # Create inline keyboard with options
    keyboard = [
        [
            InlineKeyboardButton("🟢 SPOT Market", callback_data="trade_spot"),
            InlineKeyboardButton("🔵 FUTURES Market", callback_data="trade_futures")
        ]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    # Send message with options
    await update.message.reply_text(
        "🔍 *REQUEST A HIGH-QUALITY TRADE*\n\n"
        "Please select the market type you're interested in:\n\n"
        "• SPOT: Traditional buy/sell of the actual asset\n"
        "• FUTURES: Leveraged trading with potential for higher profits\n\n"
        f"You have {trades_remaining} trade requests remaining today.",
        reply_markup=reply_markup,
        parse_mode='Markdown'
    )

async def start_trade_checker():
    """Start the trade checker if it's not already running."""
    # Check if trade checker is already running
    if is_trade_checker_running():
        logger.info("Trade checker is already running")
        return True

    try:
        # Update shared data to indicate trade checker should be running
        shared_data = load_shared_data()
        shared_data["trade_checker_running"] = True
        save_shared_data(shared_data)

        # Start trade checker process
        process = await asyncio.create_subprocess_exec(
            sys.executable, "trade_checker.py",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        logger.info("Trade checker started")
        return True
    except Exception as e:
        logger.error(f"Error starting trade checker: {e}")
        return False

async def set_commands(app):
    """Set up the bot commands for users and owner."""
    from telegram import BotCommand, BotCommandScopeChat

    # Define commands for regular users
    user_commands = [
        BotCommand("start", "Register or check your status"),
        BotCommand("help", "Show help information"),
        BotCommand("trade", "Request a high-quality trade"),
        BotCommand("cancel", "Cancel the current operation")
    ]

    # Define additional commands for the owner
    owner_commands = user_commands + [
        BotCommand("user", "Manage users"),
        BotCommand("status", "Check bot status")
    ]

    # Set default commands for all users
    await app.bot.set_my_commands(user_commands)

    # Set owner-specific commands
    owner_scope = BotCommandScopeChat(chat_id=OWNER_ID)
    await app.bot.set_my_commands(owner_commands, scope=owner_scope)

    logger.info("Bot commands have been set up")

async def init(application):
    """Initialize the bot and start the trade checker."""
    # Start the trade checker automatically
    await start_trade_checker()

    # Set up the commands
    await set_commands(application)

    # Log that initialization is complete
    logger.info("Bot initialization complete")

def main() -> None:
    """Start the bot."""
    # Ensure database files exist
    ensure_files_exist()

    # Create the Application
    application = Application.builder().token(BOT_TOKEN).build()

    # Add command handlers - keep only the essential ones
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("user", user_command))
    application.add_handler(CommandHandler("cancel", cancel_command))
    application.add_handler(CommandHandler("trade", trade_command))
    application.add_handler(CommandHandler("status", status_command))

    # Add callback query handler
    application.add_handler(CallbackQueryHandler(handle_callback_query))

    # Add message handler for conversations
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, message_handler))

    # Add error handler
    application.add_error_handler(error_handler)

    # Set up post-initialization task
    application.post_init = init

    # Run the bot until the user presses Ctrl-C
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == "__main__":
    main()
