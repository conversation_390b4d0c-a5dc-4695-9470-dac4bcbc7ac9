import logging
import requests
import json
from typing import Dict, List, Any, Optional

from config.settings import CRYPTOCOMPARE_API_KEY, COINAPI_API_KEY

logger = logging.getLogger(__name__)

class CryptoDataService:
    """Service for fetching additional cryptocurrency data from external APIs."""
    
    def __init__(self):
        """Initialize the crypto data service."""
        self.cryptocompare_api_key = CRYPTOCOMPARE_API_KEY
        self.coinapi_api_key = COINAPI_API_KEY
    
    def get_cryptocompare_price(self, symbol: str, currency: str = 'USD') -> Optional[float]:
        """
        Get current price from CryptoCompare API.
        
        Args:
            symbol: The cryptocurrency symbol (e.g., "BTC")
            currency: The currency to convert to (e.g., "USD")
            
        Returns:
            Current price or None if error
        """
        try:
            url = f"https://min-api.cryptocompare.com/data/price?fsym={symbol}&tsyms={currency}"
            headers = {
                'authorization': f"Apikey {self.cryptocompare_api_key}"
            }
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            data = response.json()
            return data.get(currency)
        except Exception as e:
            logger.error(f"Error fetching price from CryptoCompare: {e}")
            return None
    
    def get_cryptocompare_news(self, categories: str = "BTC") -> List[Dict[str, Any]]:
        """
        Get latest news from CryptoCompare API.
        
        Args:
            categories: Comma-separated list of categories (e.g., "BTC,ETH")
            
        Returns:
            List of news articles
        """
        try:
            url = f"https://min-api.cryptocompare.com/data/v2/news/?categories={categories}"
            headers = {
                'authorization': f"Apikey {self.cryptocompare_api_key}"
            }
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            data = response.json()
            return data.get('Data', [])
        except Exception as e:
            logger.error(f"Error fetching news from CryptoCompare: {e}")
            return []
    
    def get_coinapi_exchange_rates(self, base_asset: str = "BTC") -> Dict[str, float]:
        """
        Get exchange rates from CoinAPI.
        
        Args:
            base_asset: Base asset for exchange rates (e.g., "BTC")
            
        Returns:
            Dictionary of exchange rates
        """
        try:
            url = f"https://rest.coinapi.io/v1/exchangerate/{base_asset}"
            headers = {
                'X-CoinAPI-Key': self.coinapi_api_key
            }
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            data = response.json()
            
            # Extract rates
            rates = {}
            for rate in data.get('rates', []):
                asset_id = rate.get('asset_id_quote')
                rate_value = rate.get('rate')
                if asset_id and rate_value:
                    rates[asset_id] = rate_value
            
            return rates
        except Exception as e:
            logger.error(f"Error fetching exchange rates from CoinAPI: {e}")
            return {}
    
    def get_market_sentiment(self, symbol: str = "BTC") -> Dict[str, Any]:
        """
        Get market sentiment data by combining multiple sources.
        
        Args:
            symbol: The cryptocurrency symbol (e.g., "BTC")
            
        Returns:
            Dictionary with sentiment data
        """
        sentiment_data = {
            'symbol': symbol,
            'sentiment': 'neutral',
            'score': 0.0,
            'news_count': 0,
            'positive_news': 0,
            'negative_news': 0
        }
        
        try:
            # Get news articles
            news = self.get_cryptocompare_news(categories=symbol)
            
            if not news:
                return sentiment_data
            
            # Count news articles
            sentiment_data['news_count'] = len(news)
            
            # Simple sentiment analysis based on title keywords
            positive_keywords = ['bull', 'bullish', 'surge', 'soar', 'rally', 'gain', 'up', 'rise', 'positive']
            negative_keywords = ['bear', 'bearish', 'crash', 'plunge', 'drop', 'fall', 'down', 'negative']
            
            for article in news:
                title = article.get('title', '').lower()
                
                # Check for positive keywords
                if any(keyword in title for keyword in positive_keywords):
                    sentiment_data['positive_news'] += 1
                
                # Check for negative keywords
                if any(keyword in title for keyword in negative_keywords):
                    sentiment_data['negative_news'] += 1
            
            # Calculate sentiment score (-1 to 1)
            if sentiment_data['news_count'] > 0:
                positive_ratio = sentiment_data['positive_news'] / sentiment_data['news_count']
                negative_ratio = sentiment_data['negative_news'] / sentiment_data['news_count']
                sentiment_data['score'] = positive_ratio - negative_ratio
            
            # Determine sentiment
            if sentiment_data['score'] > 0.2:
                sentiment_data['sentiment'] = 'bullish'
            elif sentiment_data['score'] < -0.2:
                sentiment_data['sentiment'] = 'bearish'
            
            return sentiment_data
            
        except Exception as e:
            logger.error(f"Error calculating market sentiment: {e}")
            return sentiment_data
