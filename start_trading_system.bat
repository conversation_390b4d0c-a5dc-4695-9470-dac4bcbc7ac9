@echo off
echo ===================================================
echo        TRADING BOT DUAL TERMINAL STARTUP
echo ===================================================
echo.
echo This script will start the trading bot system with
echo two separate terminals:
echo.
echo 1. Bot Server (Telegram Bot)
echo 2. Trade Checker (Analysis Engine)
echo.
echo Press Ctrl+C in each terminal to stop the processes.
echo.
echo ===================================================
echo.
echo Starting Trading Bot System...
echo.

REM Create necessary directories
if not exist "memory" mkdir "memory"
if not exist "logs" mkdir "logs"

REM Create a memory entry for this startup
echo # System Startup Log > "memory\system_startup.md"
echo. >> "memory\system_startup.md"
echo ## Date: %date% %time% >> "memory\system_startup.md"
echo. >> "memory\system_startup.md"
echo System started with dual terminal configuration >> "memory\system_startup.md"
echo. >> "memory\system_startup.md"

REM Add information about the trade command
echo ## Usage Instructions >> "memory\system_startup.md"
echo. >> "memory\system_startup.md"
echo 1. Use the /trade command in Telegram to request a high-quality trade >> "memory\system_startup.md"
echo 2. Select between spot and futures markets >> "memory\system_startup.md"
echo 3. Wait for the trade checker to find a high-quality trade >> "memory\system_startup.md"
echo 4. Accept the trade if one is found >> "memory\system_startup.md"
echo. >> "memory\system_startup.md"

REM Start the bot server in a new terminal window
start cmd /k "title Telegram Bot Server && python bot_server.py"

REM Wait a moment to ensure the bot server starts first
timeout /t 2 /nobreak > nul

REM Start the trade checker in a new terminal window
start cmd /k "title Trade Checker && python trade_checker.py"

echo.
echo Both processes have been started in separate windows.
echo.
echo ===================================================
echo.
echo Press any key to close this window...
pause > nul
