"""
Shared data manager for communication between bot server and trade checker.
"""
import os
import json
import time
import logging
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional

# Create data directory if it doesn't exist
os.makedirs(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data'), exist_ok=True)

# Set file paths with absolute paths
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')

# Set up logging
logger = logging.getLogger(__name__)

# File path for shared data
SHARED_DATA_FILE = os.path.join(DATA_DIR, 'shared_data.json')
WAITING_TRADES_FILE = os.path.join(DATA_DIR, 'waiting_trades.json')
ACTIVE_TRADES_FILE = os.path.join(DATA_DIR, 'active_trades.json')

# Lock for thread-safe file access
file_lock = threading.Lock()

def initialize_shared_data():
    """Initialize the shared data files if they don't exist."""
    # Initialize shared data file
    if not os.path.exists(SHARED_DATA_FILE):
        default_data = {
            "waiting_trades": {
                "spot": [],
                "futures": []
            },
            "active_trades": {
                "spot": {},
                "futures": {}
            },
            "trade_stats": {
                "success_spot": 0,
                "success_future": 0,
                "failed_spot": 0,
                "failed_future": 0
            },
            "bot_status": {
                "is_running": False,
                "last_update": datetime.now().isoformat(),
                "current_pair": "",
                "current_market": "",
                "current_conditions": {}
            },
            "commands": {
                "pending": [],
                "processed": []
            }
        }
        save_shared_data(default_data)

    # Initialize waiting trades file
    if not os.path.exists(WAITING_TRADES_FILE):
        default_waiting_trades = {
            "spot": [],
            "futures": [],
            "last_update": datetime.now().isoformat()
        }
        save_waiting_trades(default_waiting_trades)

    # Initialize active trades file
    if not os.path.exists(ACTIVE_TRADES_FILE):
        default_active_trades = {
            "spot": {},
            "futures": {},
            "last_update": datetime.now().isoformat()
        }
        save_active_trades(default_active_trades)

def load_shared_data() -> Dict[str, Any]:
    """Load shared data from file."""
    with file_lock:
        try:
            if os.path.exists(SHARED_DATA_FILE):
                with open(SHARED_DATA_FILE, 'r') as f:
                    return json.load(f)
            else:
                # Return default data if file doesn't exist
                return {
                    "waiting_trades": {
                        "spot": [],
                        "futures": []
                    },
                    "active_trades": {
                        "spot": {},
                        "futures": {}
                    },
                    "trade_stats": {
                        "success_spot": 0,
                        "success_future": 0,
                        "failed_spot": 0,
                        "failed_future": 0
                    },
                    "bot_status": {
                        "is_running": False,
                        "last_update": datetime.now().isoformat(),
                        "current_pair": "",
                        "current_market": "",
                        "current_conditions": {}
                    },
                    "commands": {
                        "pending": [],
                        "processed": []
                    }
                }
        except Exception as e:
            logger.error(f"Error loading shared data: {e}")
            return {}

def save_shared_data(data: Dict[str, Any]) -> bool:
    """Save shared data to file."""
    with file_lock:
        try:
            # Update last_update timestamp
            data["bot_status"]["last_update"] = datetime.now().isoformat()

            with open(SHARED_DATA_FILE, 'w') as f:
                json.dump(data, f, indent=4)
            return True
        except Exception as e:
            logger.error(f"Error saving shared data: {e}")
            return False

def load_waiting_trades() -> Dict[str, Any]:
    """Load waiting trades from file."""
    with file_lock:
        try:
            if os.path.exists(WAITING_TRADES_FILE):
                with open(WAITING_TRADES_FILE, 'r') as f:
                    return json.load(f)
            else:
                # Return default data if file doesn't exist
                return {
                    "spot": [],
                    "futures": [],
                    "last_update": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"Error loading waiting trades: {e}")
            return {"spot": [], "futures": [], "last_update": datetime.now().isoformat()}

def save_waiting_trades(data: Dict[str, Any]) -> bool:
    """Save waiting trades to file."""
    with file_lock:
        try:
            # Update last_update timestamp
            data["last_update"] = datetime.now().isoformat()

            with open(WAITING_TRADES_FILE, 'w') as f:
                json.dump(data, f, indent=4)
            return True
        except Exception as e:
            logger.error(f"Error saving waiting trades: {e}")
            return False

def load_active_trades() -> Dict[str, Any]:
    """Load active trades from file."""
    with file_lock:
        try:
            if os.path.exists(ACTIVE_TRADES_FILE):
                with open(ACTIVE_TRADES_FILE, 'r') as f:
                    return json.load(f)
            else:
                # Return default data if file doesn't exist
                return {
                    "spot": {},
                    "futures": {},
                    "last_update": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"Error loading active trades: {e}")
            return {"spot": {}, "futures": {}, "last_update": datetime.now().isoformat()}

def save_active_trades(data: Dict[str, Any]) -> bool:
    """Save active trades to file."""
    with file_lock:
        try:
            # Update last_update timestamp
            data["last_update"] = datetime.now().isoformat()

            with open(ACTIVE_TRADES_FILE, 'w') as f:
                json.dump(data, f, indent=4)
            return True
        except Exception as e:
            logger.error(f"Error saving active trades: {e}")
            return False

def update_bot_status(status: Dict[str, Any]) -> bool:
    """Update bot status in shared data."""
    data = load_shared_data()
    data["bot_status"].update(status)
    return save_shared_data(data)

def update_waiting_trades(spot_trades: List[Dict[str, Any]], futures_trades: List[Dict[str, Any]]) -> bool:
    """Update waiting trades in both shared data and waiting trades file."""
    # Update shared data
    shared_data = load_shared_data()
    shared_data["waiting_trades"]["spot"] = spot_trades
    shared_data["waiting_trades"]["futures"] = futures_trades
    save_shared_data(shared_data)

    # Update waiting trades file
    waiting_trades = load_waiting_trades()
    waiting_trades["spot"] = spot_trades
    waiting_trades["futures"] = futures_trades
    return save_waiting_trades(waiting_trades)

def update_active_trades(spot_trades: Dict[str, Any], futures_trades: Dict[str, Any]) -> bool:
    """Update active trades in both shared data and active trades file."""
    # Update shared data
    shared_data = load_shared_data()
    shared_data["active_trades"]["spot"] = spot_trades
    shared_data["active_trades"]["futures"] = futures_trades
    save_shared_data(shared_data)

    # Update active trades file
    active_trades = load_active_trades()
    active_trades["spot"] = spot_trades
    active_trades["futures"] = futures_trades
    return save_active_trades(active_trades)

def update_trade_stats(success_spot: int, success_future: int, failed_spot: int, failed_future: int) -> bool:
    """Update trade statistics in shared data."""
    data = load_shared_data()
    data["trade_stats"]["success_spot"] = success_spot
    data["trade_stats"]["success_future"] = success_future
    data["trade_stats"]["failed_spot"] = failed_spot
    data["trade_stats"]["failed_future"] = failed_future
    return save_shared_data(data)

def add_command(command: Dict[str, Any]) -> bool:
    """Add a command to the pending commands list."""
    data = load_shared_data()

    # Add timestamp to command
    command["timestamp"] = datetime.now().isoformat()

    # Add command to pending list
    data["commands"]["pending"].append(command)

    return save_shared_data(data)

def get_pending_commands() -> List[Dict[str, Any]]:
    """Get all pending commands."""
    data = load_shared_data()
    return data["commands"]["pending"]

def mark_command_processed(command_id: str) -> bool:
    """Mark a command as processed by moving it from pending to processed."""
    data = load_shared_data()

    # Find command by ID
    for i, cmd in enumerate(data["commands"]["pending"]):
        if cmd.get("id") == command_id:
            # Move command to processed list
            data["commands"]["processed"].append(cmd)
            # Remove from pending list
            data["commands"]["pending"].pop(i)
            return save_shared_data(data)

    return False

def is_trade_checker_running() -> bool:
    """Check if the trade checker is running based on last update time."""
    data = load_shared_data()

    # Get last update time
    last_update_str = data["bot_status"]["last_update"]

    try:
        # Parse last update time
        last_update = datetime.fromisoformat(last_update_str)

        # Check if last update was within the last 2 minutes
        time_diff = (datetime.now() - last_update).total_seconds()
        return time_diff < 120  # 2 minutes
    except Exception:
        return False

def add_waiting_trade(trade: Dict[str, Any], market_type: str) -> bool:
    """Add a trade to the waiting trades list."""
    waiting_trades = load_waiting_trades()

    # Add quality score and timestamp to trade
    trade["quality_score"] = trade.get("quality_score", 0.0)
    trade["found_at"] = datetime.now().isoformat()

    # Add trade to appropriate list
    if market_type.upper() == "SPOT":
        waiting_trades["spot"].append(trade)
    else:
        waiting_trades["futures"].append(trade)

    # Update shared data as well
    shared_data = load_shared_data()
    if market_type.upper() == "SPOT":
        shared_data["waiting_trades"]["spot"].append(trade)
    else:
        shared_data["waiting_trades"]["futures"].append(trade)

    # Save both files
    save_waiting_trades(waiting_trades)
    save_shared_data(shared_data)

    return True

def get_waiting_trades(market_type: Optional[str] = None) -> List[Dict[str, Any]]:
    """Get waiting trades, optionally filtered by market type."""
    waiting_trades = load_waiting_trades()

    if market_type is None:
        # Return all waiting trades
        return waiting_trades["spot"] + waiting_trades["futures"]
    elif market_type.upper() == "SPOT":
        return waiting_trades["spot"]
    else:
        return waiting_trades["futures"]

# Initialize shared data on module import
initialize_shared_data()
