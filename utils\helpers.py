import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

def format_timestamp(timestamp: float) -> str:
    """Format a timestamp as a readable date/time string."""
    dt = datetime.fromtimestamp(timestamp)
    return dt.strftime("%Y-%m-%d %H:%M:%S")

def format_price(price: float) -> str:
    """Format a price with appropriate precision."""
    if price < 0.001:
        return f"{price:.8f}"
    elif price < 0.1:
        return f"{price:.6f}"
    elif price < 1:
        return f"{price:.4f}"
    elif price < 1000:
        return f"{price:.2f}"
    else:
        return f"{price:.1f}"

def calculate_profit_percentage(entry_price: float, target_price: float, direction: str) -> float:
    """Calculate profit percentage for a trade."""
    if direction.upper() == "BUY":
        return ((target_price - entry_price) / entry_price) * 100
    else:  # SELL
        return ((entry_price - target_price) / entry_price) * 100

def truncate_message(message: str, max_length: int = 4000) -> str:
    """Truncate a message to ensure it doesn't exceed Telegram's limits."""
    if len(message) <= max_length:
        return message
    
    # Truncate and add ellipsis
    return message[:max_length-3] + "..."

def safe_get(data: Dict[str, Any], key: str, default: Any = None) -> Any:
    """Safely get a value from a dictionary."""
    return data.get(key, default)

def format_error(e: Exception) -> str:
    """Format an exception as a readable error message."""
    return f"Error: {type(e).__name__} - {str(e)}"
