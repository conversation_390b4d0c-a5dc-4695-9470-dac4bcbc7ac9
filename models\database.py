import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple

from config.settings import (
    APPROVED_USERS_FILE,
    REJECTED_USERS_FILE,
    TRADE_HISTORY_FILE,
    MAX_TRADES_PER_DAY
)

# Define file for active trade signals
ACTIVE_SIGNALS_FILE = "active_signals.json"

def ensure_files_exist():
    """Ensure that the JSON files exist, create them if they don't."""
    for file_path in [APPROVED_USERS_FILE, REJECTED_USERS_FILE, TRADE_HISTORY_FILE, ACTIVE_SIGNALS_FILE]:
        if not os.path.exists(file_path):
            with open(file_path, 'w') as f:
                if file_path == REJECTED_USERS_FILE:
                    # For rejected users, we only store a list of user IDs
                    json.dump([], f)
                elif file_path in [TRADE_HISTORY_FILE, ACTIVE_SIGNALS_FILE]:
                    # For trade history and active signals, we store dictionaries
                    json.dump({}, f)
                else:
                    # For approved users, we store a dictionary of user details
                    json.dump({}, f)

def get_approved_users() -> Dict[str, Dict[str, Any]]:
    """Get all approved users."""
    ensure_files_exist()
    try:
        with open(APPROVED_USERS_FILE, 'r') as f:
            return json.load(f)
    except json.JSONDecodeError:
        return {}

def get_rejected_users() -> Dict[str, Dict[str, Any]]:
    """Get all rejected users with their data."""
    ensure_files_exist()
    try:
        with open(REJECTED_USERS_FILE, 'r') as f:
            return json.load(f)
    except json.JSONDecodeError:
        return {}

def is_user_approved(user_id: int) -> bool:
    """Check if a user is approved."""
    approved_users = get_approved_users()
    return str(user_id) in approved_users

def is_user_rejected(user_id: int) -> bool:
    """Check if a user is rejected."""
    rejected_users = get_rejected_users()
    return str(user_id) in rejected_users

def approve_user(user_data: Dict[str, Any]) -> bool:
    """Approve a user by adding their details to the approved users file."""
    ensure_files_exist()
    user_id = user_data.get('id')
    if not user_id:
        return False

    # Check if user is already rejected, remove from rejected list if so
    if is_user_rejected(user_id):
        remove_rejected_user(user_id)

    # Add user to approved list
    approved_users = get_approved_users()
    approved_users[str(user_id)] = user_data

    with open(APPROVED_USERS_FILE, 'w') as f:
        json.dump(approved_users, f, indent=4)

    return True

def reject_user(user_id: int) -> bool:
    """Reject a user by adding their data to the rejected users file."""
    ensure_files_exist()

    # Check if user is already approved, get their data and remove from approved list
    user_data = None
    if is_user_approved(user_id):
        user_data = get_user_data(user_id)
        remove_approved_user(user_id)

    # If we don't have user data, create minimal data
    if not user_data:
        user_data = {'id': user_id, 'first_name': 'Unknown', 'last_name': '', 'username': ''}

    # Add user to rejected list
    rejected_users = get_rejected_users()
    rejected_users[str(user_id)] = user_data

    with open(REJECTED_USERS_FILE, 'w') as f:
        json.dump(rejected_users, f, indent=4)

    return True

def remove_approved_user(user_id: int) -> bool:
    """Remove a user from the approved users file."""
    approved_users = get_approved_users()
    if str(user_id) in approved_users:
        del approved_users[str(user_id)]
        with open(APPROVED_USERS_FILE, 'w') as f:
            json.dump(approved_users, f, indent=4)
        return True
    return False

def remove_rejected_user(user_id: int) -> bool:
    """Remove a user from the rejected users file."""
    rejected_users = get_rejected_users()
    if str(user_id) in rejected_users:
        del rejected_users[str(user_id)]
        with open(REJECTED_USERS_FILE, 'w') as f:
            json.dump(rejected_users, f, indent=4)
        return True
    return False

def get_user_data(user_id: int) -> Optional[Dict[str, Any]]:
    """Get user data for an approved user."""
    approved_users = get_approved_users()
    return approved_users.get(str(user_id))

def get_trade_history() -> Dict[str, List[Dict[str, Any]]]:
    """Get the trade history for all users."""
    ensure_files_exist()
    try:
        with open(TRADE_HISTORY_FILE, 'r') as f:
            return json.load(f)
    except json.JSONDecodeError:
        return {}

def get_user_trades(user_id: int) -> List[Dict[str, Any]]:
    """Get the trade history for a specific user."""
    trade_history = get_trade_history()
    return trade_history.get(str(user_id), [])

def get_today_trades_count(user_id: int) -> int:
    """Get the number of trades a user has made today."""
    user_trades = get_user_trades(user_id)

    # Get the start of the current day in timestamp format
    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).timestamp()

    # Count trades that happened today
    today_trades = [trade for trade in user_trades if trade.get('timestamp', 0) >= today_start]
    return len(today_trades)

def can_receive_trade(user_id: int) -> Tuple[bool, int]:
    """
    Check if a user can receive a trade today.

    Returns:
        Tuple[bool, int]: A tuple containing:
            - Boolean indicating if the user can receive a trade
            - Number of trades remaining for the day
    """
    if not is_user_approved(user_id):
        return False, 0

    today_trades_count = get_today_trades_count(user_id)
    trades_remaining = MAX_TRADES_PER_DAY - today_trades_count

    return trades_remaining > 0, trades_remaining

def record_trade(user_id: int, trade_data: Dict[str, Any]) -> bool:
    """
    Record that a user has received a trade.

    Args:
        user_id: The user's ID
        trade_data: Dictionary containing trade details

    Returns:
        bool: True if the trade was recorded successfully, False otherwise
    """
    can_receive, _ = can_receive_trade(user_id)
    if not can_receive:
        return False

    # Get current trade history
    trade_history = get_trade_history()

    # Get user's trades or initialize empty list
    user_trades = trade_history.get(str(user_id), [])

    # Add current timestamp if not provided
    if 'timestamp' not in trade_data:
        trade_data['timestamp'] = datetime.now().timestamp()

    # Add trade to user's history
    user_trades.append(trade_data)

    # Update trade history
    trade_history[str(user_id)] = user_trades

    # Save to file
    with open(TRADE_HISTORY_FILE, 'w') as f:
        json.dump(trade_history, f, indent=4)

    return True

def get_trades_reset_time() -> str:
    """Get the time when the trade count will reset (midnight)."""
    tomorrow = datetime.now() + timedelta(days=1)
    midnight = tomorrow.replace(hour=0, minute=0, second=0, microsecond=0)

    # Format the time
    return midnight.strftime("%H:%M:%S")

def update_user_data(user_id: int, new_data: Dict[str, Any]) -> bool:
    """Update an approved user's data."""
    approved_users = get_approved_users()
    if str(user_id) not in approved_users:
        return False

    # Update user data
    approved_users[str(user_id)].update(new_data)

    # Save to file
    with open(APPROVED_USERS_FILE, 'w') as f:
        json.dump(approved_users, f, indent=4)

    return True

def get_active_signals() -> Dict[str, Dict[str, Any]]:
    """Get all active trade signals."""
    ensure_files_exist()
    try:
        with open(ACTIVE_SIGNALS_FILE, 'r') as f:
            return json.load(f)
    except json.JSONDecodeError:
        return {}

def save_active_signal(signal_id: str, signal_data: Dict[str, Any]) -> bool:
    """Save a trade signal to the active signals file."""
    ensure_files_exist()

    # Get current active signals
    active_signals = get_active_signals()

    # Add or update signal
    active_signals[signal_id] = signal_data

    # Save to file
    with open(ACTIVE_SIGNALS_FILE, 'w') as f:
        json.dump(active_signals, f, indent=4)

    return True

def get_active_signal(signal_id: str) -> Optional[Dict[str, Any]]:
    """Get a specific active trade signal."""
    active_signals = get_active_signals()
    return active_signals.get(signal_id)

def update_active_signal(signal_id: str, new_data: Dict[str, Any]) -> bool:
    """Update an active trade signal."""
    active_signals = get_active_signals()
    if signal_id not in active_signals:
        return False

    # Update signal data
    active_signals[signal_id].update(new_data)

    # Save to file
    with open(ACTIVE_SIGNALS_FILE, 'w') as f:
        json.dump(active_signals, f, indent=4)

    return True

def remove_active_signal(signal_id: str) -> bool:
    """Remove a trade signal from the active signals file."""
    active_signals = get_active_signals()
    if signal_id not in active_signals:
        return False

    # Remove signal
    del active_signals[signal_id]

    # Save to file
    with open(ACTIVE_SIGNALS_FILE, 'w') as f:
        json.dump(active_signals, f, indent=4)

    return True

def get_user_active_signals(user_id: int) -> Dict[str, Dict[str, Any]]:
    """Get all active trade signals for a specific user."""
    # Get all active signals
    active_signals = get_active_signals()

    # Get user's trade history
    user_trades = get_user_trades(user_id)

    # Extract signal IDs from user's trades
    user_signal_ids = [trade.get('signal_id') for trade in user_trades if 'signal_id' in trade]

    # Filter active signals to only include those the user has accepted
    user_active_signals = {
        signal_id: signal_data
        for signal_id, signal_data in active_signals.items()
        if signal_id in user_signal_ids
    }

    return user_active_signals
