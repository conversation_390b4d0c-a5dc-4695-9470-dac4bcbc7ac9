"""
Consolidated terminal display module for the trading bot.
This combines functionality from both terminal_display.py and terminal_display_new.py.
"""
import os
import sys
import time
import threading
from datetime import datetime

# ANSI color codes for terminal output
class Colors:
    """ANSI color codes for terminal output."""
    # Text colors
    BLACK = "\033[30m"
    RED = "\033[31m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    BLUE = "\033[34m"
    MAGENTA = "\033[35m"
    CYAN = "\033[36m"
    WHITE = "\033[37m"

    # Background colors
    BG_BLACK = "\033[40m"
    BG_RED = "\033[41m"
    BG_GREEN = "\033[42m"
    BG_YELLOW = "\033[43m"
    BG_BLUE = "\033[44m"
    BG_MAGENTA = "\033[45m"
    BG_CYAN = "\033[46m"
    BG_WHITE = "\033[47m"

    # Text styles
    BOLD = "\033[1m"
    UNDERLINE = "\033[4m"

    # Reset
    RESET = "\033[0m"

# Animation frames for loading indicators - using simple ASCII characters for Windows compatibility
LOADING_FRAMES = ['|', '/', '-', '\\', '|', '/', '-', '\\']

# Global variables for animation state
_animation_running = False
_animation_thread = None
_animation_text = ""
_animation_lock = threading.Lock()

# Global state for current pair being checked
_current_pair = ""
_current_market = ""
_current_conditions = {}

# Global state for waiting trades
waiting_spot_str = "None"
waiting_future_str = "None"

# Global state for active trades
active_spot_str = "None"
active_future_str = "None"

# Global state for trade statistics
success_spot = 0
success_future = 0
failed_spot = 0
failed_future = 0

# Global state for user logs
user_logs = []

# Throttling mechanism to prevent too many display updates
_last_update_time = 0
_update_interval = 0.5  # Minimum time between updates in seconds
_pending_update = False
_update_lock = threading.Lock()

def clear_terminal():
    """Clear the terminal screen."""
    # Use the appropriate command based on the OS
    if sys.platform.startswith('win'):
        os.system('cls')
    else:
        os.system('clear')

def get_terminal_size():
    """Get the terminal size (columns, lines)."""
    try:
        columns, lines = os.get_terminal_size()
        return columns, lines
    except (AttributeError, OSError):
        # Default size if unable to determine
        return 80, 24

def format_header(text):
    """Format text as a header."""
    return f"{Colors.BOLD}{Colors.BLUE}{text}{Colors.RESET}"

def format_success(text):
    """Format text as a success message."""
    return f"{Colors.GREEN}{text}{Colors.RESET}"

def format_error(text):
    """Format text as an error message."""
    return f"{Colors.RED}{text}{Colors.RESET}"

def format_warning(text):
    """Format text as a warning message."""
    return f"{Colors.YELLOW}{text}{Colors.RESET}"

def format_info(text):
    """Format text as an info message."""
    return f"{Colors.CYAN}{text}{Colors.RESET}"

def start_loading_animation(text):
    """Start a loading animation with the given text."""
    global _animation_running, _animation_thread, _animation_text

    with _animation_lock:
        _animation_text = text

        if _animation_running:
            return

        _animation_running = True
        _animation_thread = threading.Thread(target=_run_animation)
        _animation_thread.daemon = True
        _animation_thread.start()

def stop_loading_animation():
    """Stop the loading animation."""
    global _animation_running

    with _animation_lock:
        _animation_running = False

        if _animation_thread:
            _animation_thread.join(timeout=1.0)

def _run_animation():
    """Run the loading animation in a separate thread."""
    frame_index = 0

    while _animation_running:
        with _animation_lock:
            if not _animation_running:
                break

            # Get the current frame
            frame = LOADING_FRAMES[frame_index]

            # Clear the line and print the animation frame
            sys.stdout.write(f"\r{frame} {_animation_text}")
            sys.stdout.flush()

            # Update the frame index
            frame_index = (frame_index + 1) % len(LOADING_FRAMES)

        # Sleep for a short time
        time.sleep(0.1)

    # Clear the line when animation stops
    sys.stdout.write("\r" + " " * (len(_animation_text) + 2) + "\r")
    sys.stdout.flush()

def update_current_pair(pair, market_type="SPOT"):
    """Update the current pair being checked."""
    global _current_pair, _current_market
    _current_pair = pair
    _current_market = market_type

def update_condition(condition, status):
    """Update a trading condition status."""
    global _current_conditions
    _current_conditions[condition] = status

def update_waiting_trades(spot_pairs, future_pairs):
    """Update the waiting trades lists."""
    global waiting_spot_str, waiting_future_str

    if not spot_pairs:
        waiting_spot_str = "None"
    else:
        waiting_spot_str = ", ".join(spot_pairs[:5])
        if len(spot_pairs) > 5:
            waiting_spot_str += f" and {len(spot_pairs) - 5} more"

    if not future_pairs:
        waiting_future_str = "None"
    else:
        waiting_future_str = ", ".join(future_pairs[:5])
        if len(future_pairs) > 5:
            waiting_future_str += f" and {len(future_pairs) - 5} more"

def update_active_trades(spot_trades, future_trades):
    """Update the active trades display."""
    global active_spot_str, active_future_str

    if not spot_trades:
        active_spot_str = "None"
    else:
        active_spot_str = ", ".join(list(spot_trades.keys())[:5])
        if len(spot_trades) > 5:
            active_spot_str += f" and {len(spot_trades) - 5} more"

    if not future_trades:
        active_future_str = "None"
    else:
        active_future_str = ", ".join(list(future_trades.keys())[:5])
        if len(future_trades) > 5:
            active_future_str += f" and {len(future_trades) - 5} more"

def update_trade_stats(success_spot_count, success_future_count, failed_spot_count, failed_future_count):
    """Update the trade statistics."""
    global success_spot, success_future, failed_spot, failed_future
    success_spot = success_spot_count
    success_future = success_future_count
    failed_spot = failed_spot_count
    failed_future = failed_future_count

def update_user_logs(log_entry):
    """Update the user activity logs."""
    global user_logs
    user_logs.append(log_entry)

    # Keep only the last 20 logs to avoid memory issues
    if len(user_logs) > 20:
        user_logs = user_logs[-20:]

def calculate_win_rate(success_spot_count, success_future_count, failed_spot_count, failed_future_count):
    """Calculate the win rate percentage."""
    total_success = success_spot_count + success_future_count
    total_failed = failed_spot_count + failed_future_count
    total_trades = total_success + total_failed

    if total_trades == 0:
        return 0.0

    return round((total_success / total_trades) * 100, 2)

def throttled_display_update():
    """Schedule a display update with throttling to prevent too many updates."""
    global _last_update_time, _pending_update

    current_time = time.time()

    with _update_lock:
        # If we've updated recently, just mark that an update is pending
        if current_time - _last_update_time < _update_interval:
            _pending_update = True
            return

        # Otherwise, update now
        _last_update_time = current_time
        _pending_update = False

        # Run the actual display update
        _display_terminal()

        # If there's a pending update, schedule another update after the interval
        if _pending_update:
            threading.Timer(_update_interval, throttled_display_update).start()

def _display_terminal():
    """Internal function to actually update the terminal display."""
    # Clear the terminal
    clear_terminal()

    # Get terminal size
    columns, lines = get_terminal_size()

    # Get current time
    time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Display header (compact)
    header = f"{Colors.BOLD}{Colors.BG_BLUE}{Colors.WHITE} TRADING BOT ACTIVE | {time_str} {Colors.RESET}"
    print(header)

    # Calculate available space
    max_lines = lines - 10  # Reserve space for headers and footers
    current_line = 2  # Start after header

    # Display current pair being checked (if any)
    if _current_pair and _current_market:
        # Display current pair header with animated indicator
        market_color = Colors.MAGENTA if _current_market == "FUTURES" else Colors.CYAN
        frame = LOADING_FRAMES[int(time.time() * 10) % len(LOADING_FRAMES)]
        pair_header = f"{Colors.BOLD}{market_color}CHECKING {_current_market}: {_current_pair} {frame}{Colors.RESET}"
        print(pair_header)
        print("─" * min(50, columns))
        current_line += 2

        # Display conditions in a compact format
        all_valid = True
        condition_lines = []

        # Group conditions into pairs for compact display
        condition_items = list(_current_conditions.items())
        for i in range(0, len(condition_items), 2):
            line = ""
            # First condition in the pair
            condition, status = condition_items[i]
            if "valid" in status.lower() or "perfect" in status.lower():
                status_formatted = format_success("✓")
                all_valid = all_valid and True
            elif "invalid" in status.lower() or "not good" in status.lower() or "not valid" in status.lower():
                status_formatted = format_error("✗")
                all_valid = False
            elif "checking" in status.lower():
                frame = LOADING_FRAMES[int(time.time() * 10 + hash(condition) % 5) % len(LOADING_FRAMES)]
                status_formatted = format_info(frame)
                all_valid = False
            else:
                status_formatted = status
                all_valid = False

            line += f"{condition}: {status_formatted} "

            # Second condition in the pair (if exists)
            if i + 1 < len(condition_items):
                condition, status = condition_items[i + 1]
                if "valid" in status.lower() or "perfect" in status.lower():
                    status_formatted = format_success("✓")
                    all_valid = all_valid and True
                elif "invalid" in status.lower() or "not good" in status.lower() or "not valid" in status.lower():
                    status_formatted = format_error("✗")
                    all_valid = False
                elif "checking" in status.lower():
                    frame = LOADING_FRAMES[int(time.time() * 10 + hash(condition) % 5) % len(LOADING_FRAMES)]
                    status_formatted = format_info(frame)
                    all_valid = False
                else:
                    status_formatted = status
                    all_valid = False

                line += f"| {condition}: {status_formatted}"

            condition_lines.append(line)

        # Print condition lines
        for line in condition_lines:
            print(line)
            current_line += 1

        # Display conclusion
        print("─" * min(50, columns))
        if all_valid:
            print(format_success("✓ This trade meets all conditions!"))
        else:
            print(format_warning("✗ This trade does not meet all conditions"))
        current_line += 2

    # Display waiting trades (compact)
    waiting_header = f"{format_header('WAITING FOR ENTRY:')}"
    print(waiting_header)
    print(f"SPOT: {waiting_spot_str}")
    print(f"FUTURES: {waiting_future_str}")
    current_line += 3

    # Display active trades (if space allows)
    if current_line < max_lines - 5:
        active_header = f"{format_header('ACTIVE TRADES:')}"
        print(active_header)

        # Compact display of active trades
        if active_spot_str != "None":
            print(f"SPOT: {active_spot_str}")
            current_line += 1

        if active_future_str != "None":
            print(f"FUTURES: {active_future_str}")
            current_line += 1

        if active_spot_str == "None" and active_future_str == "None":
            print("None")
            current_line += 1

    # Display trade statistics (if space allows)
    if current_line < max_lines - 3:
        stats_header = f"{format_header('STATS:')}"
        print(stats_header)
        win_rate = calculate_win_rate(success_spot, success_future, failed_spot, failed_future)
        print(f"SPOT: {success_spot}/{failed_spot} | FUTURES: {success_future}/{failed_future} | Win: {win_rate}%")
        current_line += 2

    # Display user logs (if space allows and there are logs)
    if current_line < max_lines - 2 and user_logs:
        logs_header = f"{format_header('RECENT ACTIVITY:')}"
        print(logs_header)

        # Sort logs by timestamp (newest first)
        sorted_logs = sorted(user_logs, key=lambda x: x.get("timestamp", 0), reverse=True)

        # Take only the last 3 logs to keep the display compact
        recent_logs = sorted_logs[:3]

        for log in recent_logs:
            timestamp = datetime.fromtimestamp(log.get("timestamp", 0)).strftime("%H:%M:%S")
            username = log.get("username", "unknown")
            action = log.get("action", "unknown")

            if "get" in action.lower() or "success" in action.lower():
                action_formatted = format_success(action)
            elif "ignore" in action.lower() or "loss" in action.lower():
                action_formatted = format_warning(action)
            else:
                action_formatted = action

            print(f"[{timestamp}] {username}: {action_formatted}")
            current_line += 1

            # Stop if we're running out of space
            if current_line >= max_lines:
                break

    # Show checking animation at the bottom
    frame = LOADING_FRAMES[int(time.time() * 10) % len(LOADING_FRAMES)]
    print(f"\n{frame} Bot active and responsive | Press Ctrl+C to exit")

def display_terminal():
    """Public function to update the display with throttling."""
    throttled_display_update()

# Alias functions for backward compatibility
def update_pair_status(pair, market_type="SPOT"):
    """Alias for update_current_pair for backward compatibility."""
    update_current_pair(pair, market_type)

def update_condition_status(condition, status):
    """Alias for update_condition for backward compatibility."""
    update_condition(condition, status)
    # Only update display for final condition states to reduce updates
    if "valid" in status.lower() or "not valid" in status.lower():
        display_terminal()

def update_waiting_trades_status(spot_pairs, future_pairs):
    """Alias for update_waiting_trades for backward compatibility."""
    update_waiting_trades(spot_pairs, future_pairs)
    display_terminal()

def update_active_trades_status(spot_trades, future_trades):
    """Alias for update_active_trades for backward compatibility."""
    update_active_trades(spot_trades, future_trades)
    display_terminal()

def update_trade_statistics(success_spot, success_future, failed_spot, failed_future):
    """Alias for update_trade_stats for backward compatibility."""
    update_trade_stats(success_spot, success_future, failed_spot, failed_future)
    display_terminal()

def update_user_activity(log_entry):
    """Alias for update_user_logs for backward compatibility."""
    update_user_logs(log_entry)
    display_terminal()

def should_skip_pair(pair):
    """Check if a pair should be skipped (e.g., stable coin pairs)."""
    stable_coins = ["USDC", "BUSD", "TUSD", "DAI", "USDT", "FDUSD", "USDK", "USDP", "USDD"]

    # Extract the base currency from the pair (e.g., "BTC" from "BTC/USDT")
    if "/" in pair:
        base = pair.split("/")[0]
        quote = pair.split("/")[1]

        # Check if both base and quote are stable coins
        return base in stable_coins and quote in stable_coins

    return False
