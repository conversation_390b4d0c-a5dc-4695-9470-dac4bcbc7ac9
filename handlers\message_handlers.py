"""
Message handlers for the Telegram bot.
"""
import logging
from telegram import Update
from telegram.ext import ContextTypes

logger = logging.getLogger(__name__)

async def message_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle text messages."""
    # Get user ID and message text
    user_id = update.effective_user.id
    message_text = update.message.text
    
    # Check if we're waiting for a message to send to another user
    if context.user_data.get('waiting_for_message'):
        # Get the target user ID
        target_user_id = context.user_data.get('message_to_user_id')
        
        if target_user_id:
            try:
                # Send the message to the target user
                await context.bot.send_message(
                    chat_id=target_user_id,
                    text=f"*Message from Admin:*\n\n{message_text}",
                    parse_mode='Markdown'
                )
                
                # Confirm to the admin
                await update.message.reply_text(
                    f"✅ Message sent to user {target_user_id}."
                )
                
                # Reset conversation state
                context.user_data['waiting_for_message'] = False
                context.user_data['message_to_user_id'] = None
                
                # Log the action
                logger.info(f"Admin sent message to user {target_user_id}")
                
            except Exception as e:
                # Handle errors
                await update.message.reply_text(
                    f"❌ Error sending message: {str(e)}"
                )
                logger.error(f"Error sending message to user {target_user_id}: {e}")
        else:
            # No target user ID
            await update.message.reply_text(
                "❌ No target user specified. Operation cancelled."
            )
            
            # Reset conversation state
            context.user_data['waiting_for_message'] = False
            
        return
    
    # Default response for other messages
    await update.message.reply_text(
        "I don't understand that command. Please use one of the available commands:\n"
        "/start - Register or check your status\n"
        "/help - Show help information\n"
        "/trade - Request a high-quality trade\n"
        "/cancel - Cancel the current operation"
    )
