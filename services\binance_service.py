import time
import logging
import pandas as pd
import ccxt
from typing import Dict, List, Any, Optional

from config.settings import BINANCE_API_KEY, BINANCE_SECRET_KEY, AVOID_SPOT_TOKENS_ABOVE_PRICE

logger = logging.getLogger(__name__)

class ExchangeService:
    """Service for interacting with cryptocurrency exchanges using CCXT."""

    def __init__(self):
        """Initialize the exchange service with Binance."""
        self.exchange = ccxt.binance({
            'apiKey': BINANCE_API_KEY,
            'secret': BINANCE_SECRET_KEY,
            'enableRateLimit': True,  # Important to avoid rate limit issues
            'options': {
                'defaultType': 'spot',  # Default to spot market
                'adjustForTimeDifference': True,  # Adjust for time difference
            }
        })

        # Synchronize time with Binance server
        self._sync_time()

    def _sync_time(self):
        """Synchronize local time with Binance server time."""
        try:
            # Get server time directly from REST API to avoid circular dependencies
            import requests
            response = requests.get('https://api.binance.com/api/v3/time')
            if response.status_code == 200:
                server_time = response.json()['serverTime']

                # Calculate time difference
                local_time = int(time.time() * 1000)
                time_diff = server_time - local_time

                # Log time difference
                if abs(time_diff) > 1000:  # If difference is more than 1 second
                    logger.info(f"Time difference with Binance server: {time_diff} ms")

                # Set time offset in CCXT
                self.exchange.options['adjustForTimeDifference'] = True
                self.exchange.options['timeDifference'] = time_diff

                # Apply nonce offset directly to avoid any issues
                if hasattr(self.exchange, 'nonce'):
                    self.exchange.nonce = lambda: int(time.time() * 1000) + time_diff

                return True
            else:
                logger.error(f"Error getting server time: {response.status_code} {response.text}")
                return False
        except Exception as e:
            logger.error(f"Error synchronizing time with Binance: {e}")
            return False

    def get_exchange_info(self) -> Dict[str, Any]:
        """Get exchange information."""
        try:
            # Sync time before making API call
            self._sync_time()
            return self.exchange.load_markets(reload=True)
        except Exception as e:
            logger.error(f"Error loading markets: {e}")
            return {}

    def get_ticker_prices(self) -> Dict[str, Any]:
        """Get ticker prices for all symbols."""
        try:
            # Sync time before making API call
            self._sync_time()
            return self.exchange.fetch_tickers()
        except Exception as e:
            logger.error(f"Error fetching tickers: {e}")
            return {}

    def get_ticker_24hr(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        Get 24-hour ticker for a symbol or all symbols.

        Args:
            symbol: The trading pair symbol (e.g., "BTC/USDT") or None for all

        Returns:
            Dictionary with ticker information
        """
        try:
            # Sync time before making API call
            self._sync_time()
            if symbol:
                return self.exchange.fetch_ticker(symbol)
            else:
                return self.exchange.fetch_tickers()
        except Exception as e:
            logger.error(f"Error fetching ticker: {e}")
            return {}

    def get_ohlcv(self, symbol: str, timeframe: str = '1h', limit: int = 100) -> pd.DataFrame:
        """
        Get OHLCV (Open, High, Low, Close, Volume) data for a symbol.

        Args:
            symbol: The trading pair symbol (e.g., "BTC/USDT")
            timeframe: Timeframe interval (e.g., "1m", "5m", "1h", "1d")
            limit: Number of candles to retrieve

        Returns:
            DataFrame with OHLCV data
        """
        try:
            # Sync time before making API call
            self._sync_time()

            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)

            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

            # Convert timestamp to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

            # Set timestamp as index
            df.set_index('timestamp', inplace=True)

            return df
        except Exception as e:
            logger.error(f"Error fetching OHLCV data for {symbol}: {e}")
            return pd.DataFrame()

    def get_account_info(self) -> Dict[str, Any]:
        """Get account information."""
        try:
            # Sync time before making API call
            self._sync_time()
            return self.exchange.fetch_balance()
        except Exception as e:
            logger.error(f"Error fetching account info: {e}")
            return {}

    def get_viable_trading_pairs(self) -> List[str]:
        """
        Get a list of viable trading pairs based on criteria:
        - For spot: avoid tokens above $20
        - Must have sufficient volume
        - Must be trading against USDT
        - Limited to top 150 tokens by volume

        Returns:
            List of viable trading pair symbols
        """
        try:
            # Sync time before making API calls
            self._sync_time()

            # Load markets
            markets = self.exchange.load_markets()

            # Get tickers for volume and price data
            tickers = self.exchange.fetch_tickers()

            # Create a list of pairs with their volume for sorting
            pairs_with_volume = []

            for symbol, market in markets.items():
                # Check if it's trading against USDT
                if not symbol.endswith('/USDT'):
                    continue

                # Check if it's active
                if not market['active']:
                    continue

                # Get ticker info
                ticker = tickers.get(symbol, {})
                if not ticker:
                    continue

                # Check volume (minimum $100,000 in 24h)
                volume = ticker.get('quoteVolume', 0)
                if volume < 100000:
                    continue

                # For spot, check price
                price = ticker.get('last', 0)
                if price > AVOID_SPOT_TOKENS_ABOVE_PRICE:
                    # Still include it for futures trading
                    continue

                # Add to list with volume for sorting
                pairs_with_volume.append((symbol, volume))

            # Sort by volume (highest first)
            pairs_with_volume.sort(key=lambda x: x[1], reverse=True)

            # Take only the top 150 pairs
            top_pairs = pairs_with_volume[:150]

            # Extract just the symbols
            viable_pairs = [pair[0] for pair in top_pairs]

            logger.info(f"Selected top {len(viable_pairs)} trading pairs by volume")
            return viable_pairs

        except Exception as e:
            logger.error(f"Error getting viable trading pairs: {e}")
            return []

    def get_current_price(self, symbol: str) -> float:
        """
        Get the current price for a symbol.

        Args:
            symbol: The trading pair symbol (e.g., "BTC/USDT")

        Returns:
            Current price as float, or 0.0 if error
        """
        try:
            # Sync time before making API call
            self._sync_time()
            ticker = self.exchange.fetch_ticker(symbol)
            return ticker.get('last', 0.0)
        except Exception as e:
            logger.error(f"Error fetching current price for {symbol}: {e}")
            return 0.0

    def get_recommended_leverage(self, symbol: str) -> int:
        """
        Get recommended leverage for a futures trading pair based on volatility.

        Args:
            symbol: The trading pair symbol (e.g., "BTC/USDT")

        Returns:
            Recommended leverage (1-20)
        """
        try:
            # Sync time before making API call
            self._sync_time()

            # Switch to futures market
            self.exchange.options['defaultType'] = 'future'

            # Get OHLCV data
            df = self.get_ohlcv(symbol, '1d', 14)  # 14 days

            if df.empty:
                return 5  # Default leverage

            # Calculate daily price changes
            df['daily_change'] = df['close'].pct_change().abs()

            # Calculate average daily change (volatility)
            avg_daily_change = df['daily_change'].mean()

            # Switch back to spot market
            self.exchange.options['defaultType'] = 'spot'

            # Determine leverage based on volatility
            # Higher volatility = lower leverage
            if avg_daily_change > 0.1:  # Very volatile
                return 2
            elif avg_daily_change > 0.05:
                return 5
            elif avg_daily_change > 0.03:
                return 10
            elif avg_daily_change > 0.01:
                return 15
            else:
                return 20

        except Exception as e:
            logger.error(f"Error calculating recommended leverage: {e}")
            # Switch back to spot market
            self.exchange.options['defaultType'] = 'spot'
            return 5  # Default leverage

