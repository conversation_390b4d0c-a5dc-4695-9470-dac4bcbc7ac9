import logging
import random
import pandas as pd
import pandas_ta as ta
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from io import BytesIO

from services.binance_service import ExchangeService
from services.crypto_data_service import CryptoDataService
from models.trade import TradeSignal
from config.settings import TRADING_CONCEPTS, MIN_WIN_RATE_PERCENT

logger = logging.getLogger(__name__)

class TradingStrategy:
    """Service for analyzing trading strategies and generating signals."""

    def __init__(self, exchange=None):
        self.exchange = exchange if exchange else ExchangeService()
        self.crypto_data = CryptoDataService()

    def analyze_ict(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """
        Analyze Institutional Candle Theory (ICT) concepts using pandas-ta.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            Tuple of (concepts_met, explanation)
        """
        # This is a simplified implementation for demonstration
        # In a real system, this would involve complex analysis

        if len(df) < 50:
            return False, ""

        # Check for liquidity sweep (a common ICT concept)
        recent_low = df['low'][-20:-1].min()
        recent_high = df['high'][-20:-1].max()

        latest_low = df['low'].iloc[-1]
        latest_high = df['high'].iloc[-1]
        latest_close = df['close'].iloc[-1]

        # Simulate ICT analysis
        liquidity_sweep = (latest_low < recent_low and latest_close > recent_low) or \
                         (latest_high > recent_high and latest_close < recent_high)

        if liquidity_sweep:
            explanation = "ICT Liquidity Sweep detected: Price swept a significant level before reversing."
            return True, explanation

        # Check for fair value gap (another ICT concept)
        for i in range(len(df) - 3, len(df) - 20, -1):
            if i < 0 or i+2 >= len(df):
                continue

            if df['high'].iloc[i] < df['low'].iloc[i+2]:  # Bullish FVG
                explanation = "ICT Fair Value Gap (Bullish) detected: Price is likely to fill this gap."
                return True, explanation
            elif df['low'].iloc[i] > df['high'].iloc[i+2]:  # Bearish FVG
                explanation = "ICT Fair Value Gap (Bearish) detected: Price is likely to fill this gap."
                return True, explanation

        # Check for order blocks using volume analysis
        df['volume_ma'] = df['volume'].rolling(10).mean()
        high_volume_bars = df[df['volume'] > 1.5 * df['volume_ma']]

        if not high_volume_bars.empty:
            last_high_vol_idx = high_volume_bars.index[-1]
            last_high_vol_pos = df.index.get_loc(last_high_vol_idx)

            if last_high_vol_pos < len(df) - 3:  # At least 3 bars ago
                # Check if price moved significantly after the high volume bar
                if df['close'].iloc[last_high_vol_pos+3] > df['close'].iloc[last_high_vol_pos] * 1.02:
                    explanation = "ICT Order Block detected: High volume followed by significant price movement."
                    return True, explanation

        return False, ""

    def analyze_smc(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """
        Analyze Smart Money Concepts (SMC) using pandas-ta.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            Tuple of (concepts_met, explanation)
        """
        # Simplified implementation
        if len(df) < 50:
            return False, ""

        # Manual check for engulfing patterns instead of using TA-Lib
        for i in range(len(df) - 2, len(df) - 1):
            if i < 0:
                continue

            # Check for bullish engulfing
            if (df['open'].iloc[i] > df['close'].iloc[i] and  # Previous candle is bearish
                df['open'].iloc[i+1] < df['close'].iloc[i+1] and  # Current candle is bullish
                df['open'].iloc[i+1] < df['open'].iloc[i] and  # Current open below previous open
                df['close'].iloc[i+1] > df['close'].iloc[i]):  # Current close above previous close

                explanation = "SMC Bullish Engulfing Pattern detected: Strong buying pressure expected."
                return True, explanation

            # Check for bearish engulfing
            if (df['open'].iloc[i] < df['close'].iloc[i] and  # Previous candle is bullish
                df['open'].iloc[i+1] > df['close'].iloc[i+1] and  # Current candle is bearish
                df['open'].iloc[i+1] > df['open'].iloc[i] and  # Current open above previous open
                df['close'].iloc[i+1] < df['close'].iloc[i]):  # Current close below previous close

                explanation = "SMC Bearish Engulfing Pattern detected: Strong selling pressure expected."
                return True, explanation

        # Check for order block (a key SMC concept)
        for i in range(len(df) - 10, len(df) - 1):
            if i < 0 or i+2 >= len(df):
                continue

            # Bullish order block
            if (df['close'].iloc[i] > df['open'].iloc[i] and
                df['close'].iloc[i+1] < df['open'].iloc[i+1] and
                df['low'].iloc[i+1] < df['low'].iloc[i]):

                if df['low'].iloc[i+2:].min() > df['low'].iloc[i]:
                    explanation = "SMC Bullish Order Block detected: Strong buying pressure expected."
                    return True, explanation

            # Bearish order block
            if (df['close'].iloc[i] < df['open'].iloc[i] and
                df['close'].iloc[i+1] > df['open'].iloc[i+1] and
                df['high'].iloc[i+1] > df['high'].iloc[i]):

                if df['high'].iloc[i+2:].max() < df['high'].iloc[i]:
                    explanation = "SMC Bearish Order Block detected: Strong selling pressure expected."
                    return True, explanation

        # Calculate and check for supply/demand zones
        # Add volume-weighted analysis for better SMC detection
        df['vwap'] = ta.vwap(df['high'], df['low'], df['close'], df['volume'])

        # Check if price is near VWAP
        last_close = df['close'].iloc[-1]
        last_vwap = df['vwap'].iloc[-1]

        if abs(last_close - last_vwap) / last_vwap < 0.005:  # Within 0.5% of VWAP
            if last_close > last_vwap:
                explanation = "SMC Support at VWAP detected: Price finding support at value area."
                return True, explanation
            else:
                explanation = "SMC Resistance at VWAP detected: Price finding resistance at value area."
                return True, explanation

        return False, ""

    def analyze_price_action(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """
        Analyze Price Action (PA) using pandas-ta.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            Tuple of (concepts_met, explanation)
        """
        # Simplified implementation
        if len(df) < 20:
            return False, ""

        # Manual check for engulfing patterns
        for i in range(len(df) - 2, len(df) - 1):
            if i < 0:
                continue

            # Check for bullish engulfing
            if (df['open'].iloc[i] > df['close'].iloc[i] and  # Previous candle is bearish
                df['open'].iloc[i+1] < df['close'].iloc[i+1] and  # Current candle is bullish
                df['open'].iloc[i+1] < df['open'].iloc[i] and  # Current open below previous open
                df['close'].iloc[i+1] > df['close'].iloc[i]):  # Current close above previous close

                explanation = "Bullish Engulfing Pattern detected: Strong reversal signal."
                return True, explanation

            # Check for bearish engulfing
            if (df['open'].iloc[i] < df['close'].iloc[i] and  # Previous candle is bullish
                df['open'].iloc[i+1] > df['close'].iloc[i+1] and  # Current candle is bearish
                df['open'].iloc[i+1] > df['open'].iloc[i] and  # Current open above previous open
                df['close'].iloc[i+1] < df['close'].iloc[i]):  # Current close below previous close

                explanation = "Bearish Engulfing Pattern detected: Strong reversal signal."
                return True, explanation

        # Manual check for hammer/hanging man
        for i in range(len(df) - 2, len(df) - 1):
            if i < 0:
                continue

            body_size = abs(df['close'].iloc[i] - df['open'].iloc[i])
            upper_wick = df['high'].iloc[i] - max(df['open'].iloc[i], df['close'].iloc[i])
            lower_wick = min(df['open'].iloc[i], df['close'].iloc[i]) - df['low'].iloc[i]

            # Hammer: small body, little or no upper wick, long lower wick
            if (body_size < (df['high'].iloc[i] - df['low'].iloc[i]) * 0.3 and  # Small body
                upper_wick < body_size * 0.5 and  # Little or no upper wick
                lower_wick > body_size * 2):  # Long lower wick

                # Hammer in downtrend
                if df['close'].iloc[i-5:i].mean() > df['close'].iloc[i]:
                    explanation = "Bullish Hammer detected: Potential reversal from downtrend."
                    return True, explanation
                # Hanging man in uptrend
                else:
                    explanation = "Bearish Hanging Man detected: Potential reversal from uptrend."
                    return True, explanation

        # Manual check for doji
        for i in range(len(df) - 2, len(df) - 1):
            if i < 0:
                continue

            body_size = abs(df['close'].iloc[i] - df['open'].iloc[i])
            candle_range = df['high'].iloc[i] - df['low'].iloc[i]

            # Doji: very small body compared to range
            if body_size < candle_range * 0.1:
                explanation = "Doji Pattern detected: Market indecision, potential reversal."
                return True, explanation

        # Calculate and check for pin bars manually if not detected by TA-Lib
        for i in range(len(df) - 5, len(df) - 1):
            if i < 0:
                continue

            body_size = abs(df['close'].iloc[i] - df['open'].iloc[i])
            upper_wick = df['high'].iloc[i] - max(df['open'].iloc[i], df['close'].iloc[i])
            lower_wick = min(df['open'].iloc[i], df['close'].iloc[i]) - df['low'].iloc[i]

            # Bullish pin bar
            if lower_wick > 2 * body_size and upper_wick < body_size:
                explanation = "Bullish Pin Bar detected: Rejection of lower prices."
                return True, explanation

            # Bearish pin bar
            if upper_wick > 2 * body_size and lower_wick < body_size:
                explanation = "Bearish Pin Bar detected: Rejection of higher prices."
                return True, explanation

        return False, ""

    def analyze_support_resistance(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """
        Analyze Support and Resistance levels using pandas-ta.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            Tuple of (concepts_met, explanation)
        """
        # Simplified implementation
        if len(df) < 50:
            return False, ""

        # Calculate pivot points with error handling
        try:
            df.ta.bbands(append=True)  # Bollinger Bands
        except Exception as e:
            logger.warning(f"Error calculating Bollinger Bands: {e}")
            # Calculate Bollinger Bands manually
            df['BBM_5_2.0'] = df['close'].rolling(window=5).mean()
            df['BBU_5_2.0'] = df['BBM_5_2.0'] + (df['close'].rolling(window=5).std() * 2)
            df['BBL_5_2.0'] = df['BBM_5_2.0'] - (df['close'].rolling(window=5).std() * 2)

        try:
            df.ta.sma(length=50, append=True)  # 50-period SMA
        except Exception as e:
            logger.warning(f"Error calculating SMA_50: {e}")
            # Calculate SMA manually if pandas-ta fails
            df['SMA_50'] = df['close'].rolling(window=50).mean()

        try:
            df.ta.sma(length=200, append=True)  # 200-period SMA
        except Exception as e:
            logger.warning(f"Error calculating SMA_200: {e}")
            # Calculate SMA manually if pandas-ta fails
            df['SMA_200'] = df['close'].rolling(window=200).mean()

        # Current price
        current_price = df['close'].iloc[-1]

        # Check if all required columns exist
        required_columns = ['BBU_5_2.0', 'BBL_5_2.0', 'SMA_50', 'SMA_200']
        for col in required_columns:
            if col not in df.columns:
                logger.warning(f"Required column {col} not found in DataFrame")
                return False, ""

        # Check if price is near Bollinger Bands
        upper_band = df['BBU_5_2.0'].iloc[-1]
        lower_band = df['BBL_5_2.0'].iloc[-1]

        # Check if price is near key moving averages
        sma_50 = df['SMA_50'].iloc[-1]
        sma_200 = df['SMA_200'].iloc[-1]

        # Check for support/resistance at Bollinger Bands
        if abs(current_price - upper_band) / upper_band < 0.005:  # Within 0.5% of upper band
            explanation = f"Price testing resistance at upper Bollinger Band ({upper_band:.8f})"
            return True, explanation

        if abs(current_price - lower_band) / lower_band < 0.005:  # Within 0.5% of lower band
            explanation = f"Price testing support at lower Bollinger Band ({lower_band:.8f})"
            return True, explanation

        # Check for support/resistance at moving averages
        if abs(current_price - sma_50) / sma_50 < 0.005:  # Within 0.5% of 50 SMA
            if current_price > sma_50:
                explanation = f"Price finding support at 50-period SMA ({sma_50:.8f})"
            else:
                explanation = f"Price finding resistance at 50-period SMA ({sma_50:.8f})"
            return True, explanation

        if abs(current_price - sma_200) / sma_200 < 0.005:  # Within 0.5% of 200 SMA
            if current_price > sma_200:
                explanation = f"Price finding support at 200-period SMA ({sma_200:.8f})"
            else:
                explanation = f"Price finding resistance at 200-period SMA ({sma_200:.8f})"
            return True, explanation

        # Find swing highs and lows
        df['swing_high'] = df['high'].rolling(5, center=True).apply(
            lambda x: 1 if x.iloc[2] == max(x) else 0, raw=False
        )
        df['swing_low'] = df['low'].rolling(5, center=True).apply(
            lambda x: 1 if x.iloc[2] == min(x) else 0, raw=False
        )

        # Get recent swing points
        recent_swing_highs = df[df['swing_high'] == 1].iloc[-10:]
        recent_swing_lows = df[df['swing_low'] == 1].iloc[-10:]

        # Check if current price is near any swing high/low
        for _, row in recent_swing_highs.iterrows():
            level_price = row['high']
            if abs(current_price - level_price) / level_price < 0.01:  # Within 1% of level
                if current_price < level_price:
                    explanation = f"Price rejected from resistance level at {level_price:.8f}"
                    return True, explanation

        for _, row in recent_swing_lows.iterrows():
            level_price = row['low']
            if abs(current_price - level_price) / level_price < 0.01:  # Within 1% of level
                if current_price > level_price:
                    explanation = f"Price bounced off support level at {level_price:.8f}"
                    return True, explanation

        return False, ""

    def analyze_patterns(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """
        Analyze chart patterns using pandas-ta.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            Tuple of (concepts_met, explanation)
        """
        # Simplified implementation
        if len(df) < 30:
            return False, ""

        # Manual pattern detection instead of using TA-Lib
        # We'll focus on simpler patterns that can be detected without complex algorithms

        # Find local highs and lows
        window_size = 5
        df['local_high'] = df['high'].rolling(window=window_size, center=True).max()
        df['local_low'] = df['low'].rolling(window=window_size, center=True).min()

        # Simple double top detection
        if len(df) > 20:
            recent_highs = []
            for i in range(len(df) - 20, len(df) - 1):
                if i < 0:
                    continue
                if df['high'].iloc[i] == df['local_high'].iloc[i]:
                    recent_highs.append((i, df['high'].iloc[i]))

            # Check if we have at least 2 similar highs
            if len(recent_highs) >= 2:
                # Sort by price (highest first)
                recent_highs.sort(key=lambda x: x[1], reverse=True)

                # Check if top 2 highs are within 1% of each other
                if abs(recent_highs[0][1] - recent_highs[1][1]) / recent_highs[0][1] < 0.01:
                    # Check if there's a significant drop between them
                    idx1, idx2 = recent_highs[0][0], recent_highs[1][0]
                    min_between = df['low'].iloc[min(idx1, idx2):max(idx1, idx2)].min()

                    if (recent_highs[0][1] - min_between) / recent_highs[0][1] > 0.02:  # 2% drop
                        explanation = "Double Top pattern detected: Potential reversal from bullish to bearish."
                        return True, explanation

        # Simple double bottom detection
        if len(df) > 20:
            recent_lows = []
            for i in range(len(df) - 20, len(df) - 1):
                if i < 0:
                    continue
                if df['low'].iloc[i] == df['local_low'].iloc[i]:
                    recent_lows.append((i, df['low'].iloc[i]))

            # Check if we have at least 2 similar lows
            if len(recent_lows) >= 2:
                # Sort by price (lowest first)
                recent_lows.sort(key=lambda x: x[1])

                # Check if bottom 2 lows are within 1% of each other
                if abs(recent_lows[0][1] - recent_lows[1][1]) / recent_lows[0][1] < 0.01:
                    # Check if there's a significant rise between them
                    idx1, idx2 = recent_lows[0][0], recent_lows[1][0]
                    max_between = df['high'].iloc[min(idx1, idx2):max(idx1, idx2)].max()

                    if (max_between - recent_lows[0][1]) / recent_lows[0][1] > 0.02:  # 2% rise
                        explanation = "Double Bottom pattern detected: Potential reversal from bearish to bullish."
                        return True, explanation

        # Calculate additional technical indicators
        df.ta.macd(append=True)
        df.ta.rsi(append=True)

        # Check for MACD crossover
        if df['MACD_12_26_9'].iloc[-2] < 0 and df['MACD_12_26_9'].iloc[-1] > 0:
            explanation = "MACD crossed above zero: Bullish momentum signal."
            return True, explanation
        elif df['MACD_12_26_9'].iloc[-2] > 0 and df['MACD_12_26_9'].iloc[-1] < 0:
            explanation = "MACD crossed below zero: Bearish momentum signal."
            return True, explanation

        # Check for RSI conditions
        if df['RSI_14'].iloc[-1] < 30:
            explanation = "RSI below 30: Oversold condition, potential bullish reversal."
            return True, explanation
        elif df['RSI_14'].iloc[-1] > 70:
            explanation = "RSI above 70: Overbought condition, potential bearish reversal."
            return True, explanation

        return False, ""

    def calculate_targets_and_stop(self, df: pd.DataFrame, direction: str) -> Tuple[float, List[float]]:
        """
        Calculate appropriate targets and stop loss based on market structure and concepts.

        Args:
            df: DataFrame with OHLCV data
            direction: "BUY" or "SELL"

        Returns:
            Tuple of (stop_loss, targets)
        """
        current_price = df['close'].iloc[-1]

        # Calculate key levels using pandas-ta with error handling
        try:
            df.ta.atr(length=14, append=True)  # ATR for volatility-based stops
        except Exception as e:
            logger.warning(f"Error calculating ATR: {e}")
            # Use a default ATR value if calculation fails
            df['ATRr_14'] = df['close'].iloc[-1] * 0.02  # Default to 2% of price

        try:
            df.ta.bbands(append=True)  # Bollinger Bands
        except Exception as e:
            logger.warning(f"Error calculating Bollinger Bands: {e}")
            # Continue without Bollinger Bands

        try:
            df.ta.sma(length=50, append=True)  # 50-period SMA
        except Exception as e:
            logger.warning(f"Error calculating SMA_50: {e}")
            # Calculate SMA manually if pandas-ta fails
            df['SMA_50'] = df['close'].rolling(window=50).mean()

        try:
            df.ta.sma(length=200, append=True)  # 200-period SMA
        except Exception as e:
            logger.warning(f"Error calculating SMA_200: {e}")
            # Calculate SMA manually if pandas-ta fails
            df['SMA_200'] = df['close'].rolling(window=200).mean()

        # Find swing highs and lows for market structure
        df['swing_high'] = df['high'].rolling(5, center=True).apply(
            lambda x: 1 if x.iloc[2] == max(x) else 0, raw=False
        )
        df['swing_low'] = df['low'].rolling(5, center=True).apply(
            lambda x: 1 if x.iloc[2] == min(x) else 0, raw=False
        )

        # Get recent swing points
        recent_swing_highs = df[df['swing_high'] == 1].iloc[-10:]
        recent_swing_lows = df[df['swing_low'] == 1].iloc[-10:]

        # Get key moving averages
        sma_50 = df['SMA_50'].iloc[-1]
        sma_200 = df['SMA_200'].iloc[-1]

        if direction == "BUY":
            # For buy signals, use market structure for stop loss
            # Find the most recent swing low below current price
            valid_swing_lows = recent_swing_lows[recent_swing_lows['low'] < current_price]

            if not valid_swing_lows.empty:
                # Use the most recent swing low as stop loss
                stop_loss = valid_swing_lows['low'].iloc[-1]
                logger.info(f"Using swing low at {stop_loss} as stop loss")
            else:
                # If no valid swing low, use key moving average
                if current_price > sma_50:
                    stop_loss = sma_50
                    logger.info(f"Using 50 SMA at {stop_loss} as stop loss")
                elif current_price > sma_200:
                    stop_loss = sma_200
                    logger.info(f"Using 200 SMA at {stop_loss} as stop loss")
                else:
                    # Last resort: use ATR-based stop
                    atr = df['ATRr_14'].iloc[-1]
                    stop_loss = current_price - (atr * 2)
                    logger.info(f"Using ATR-based stop at {stop_loss}")

            # Calculate targets based on market structure
            # Find swing highs above current price for resistance levels
            valid_swing_highs = recent_swing_highs[recent_swing_highs['high'] > current_price]

            if len(valid_swing_highs) >= 3:
                # Use swing highs as targets
                sorted_levels = sorted(valid_swing_highs['high'].tolist())
                targets = [
                    sorted_levels[0],  # First resistance
                    sorted_levels[min(1, len(sorted_levels)-1)],  # Second resistance
                    sorted_levels[min(2, len(sorted_levels)-1)]   # Third resistance
                ]
                logger.info(f"Using swing highs as targets: {targets}")
            else:
                # Use Bollinger Bands and key levels
                upper_band = df['BBU_5_2.0'].iloc[-1]

                # Calculate distance from current price to stop loss
                risk = current_price - stop_loss

                # Set targets based on structure, not fixed percentages
                targets = [
                    upper_band,  # First target: Upper Bollinger Band
                    current_price + (risk * 2),  # Second target: 2x the risk distance
                    current_price + (risk * 3)   # Third target: 3x the risk distance
                ]
                logger.info(f"Using technical levels as targets: {targets}")

        else:  # SELL
            # For sell signals, use market structure for stop loss
            # Find the most recent swing high above current price
            valid_swing_highs = recent_swing_highs[recent_swing_highs['high'] > current_price]

            if not valid_swing_highs.empty:
                # Use the most recent swing high as stop loss
                stop_loss = valid_swing_highs['high'].iloc[-1]
                logger.info(f"Using swing high at {stop_loss} as stop loss")
            else:
                # If no valid swing high, use key moving average
                if current_price < sma_50:
                    stop_loss = sma_50
                    logger.info(f"Using 50 SMA at {stop_loss} as stop loss")
                elif current_price < sma_200:
                    stop_loss = sma_200
                    logger.info(f"Using 200 SMA at {stop_loss} as stop loss")
                else:
                    # Last resort: use ATR-based stop
                    atr = df['ATRr_14'].iloc[-1]
                    stop_loss = current_price + (atr * 2)
                    logger.info(f"Using ATR-based stop at {stop_loss}")

            # Calculate targets based on market structure
            # Find swing lows below current price for support levels
            valid_swing_lows = recent_swing_lows[recent_swing_lows['low'] < current_price]

            if len(valid_swing_lows) >= 3:
                # Use swing lows as targets
                sorted_levels = sorted(valid_swing_lows['low'].tolist(), reverse=True)
                targets = [
                    sorted_levels[0],  # First support
                    sorted_levels[min(1, len(sorted_levels)-1)],  # Second support
                    sorted_levels[min(2, len(sorted_levels)-1)]   # Third support
                ]
                logger.info(f"Using swing lows as targets: {targets}")
            else:
                # Use Bollinger Bands and key levels
                lower_band = df['BBL_5_2.0'].iloc[-1]

                # Calculate distance from current price to stop loss
                risk = stop_loss - current_price

                # Set targets based on structure, not fixed percentages
                targets = [
                    lower_band,  # First target: Lower Bollinger Band
                    current_price - (risk * 2),  # Second target: 2x the risk distance
                    current_price - (risk * 3)   # Third target: 3x the risk distance
                ]
                logger.info(f"Using technical levels as targets: {targets}")

        return stop_loss, targets

    def generate_chart(self, df: pd.DataFrame, symbol: str) -> Optional[BytesIO]:
        """
        Generate a chart image for the trading signal.

        Args:
            df: DataFrame with OHLCV data
            symbol: The trading pair symbol

        Returns:
            BytesIO object containing the chart image
        """
        try:
            # Create a figure
            plt.figure(figsize=(12, 8))

            # Plot candlestick chart
            plt.subplot(2, 1, 1)

            # Plot OHLC
            plt.plot(df.index, df['close'], label='Close')

            # Plot indicators
            if 'BBU_5_2.0' in df.columns:
                plt.plot(df.index, df['BBU_5_2.0'], 'r--', label='Upper BB')
                plt.plot(df.index, df['BBM_5_2.0'], 'g--', label='Middle BB')
                plt.plot(df.index, df['BBL_5_2.0'], 'r--', label='Lower BB')

            if 'SMA_50' in df.columns:
                plt.plot(df.index, df['SMA_50'], 'b-', label='SMA 50')

            if 'SMA_200' in df.columns:
                plt.plot(df.index, df['SMA_200'], 'y-', label='SMA 200')

            plt.title(f'{symbol} Price Chart')
            plt.ylabel('Price')
            plt.legend()
            plt.grid(True)

            # Plot volume
            plt.subplot(2, 1, 2)
            plt.bar(df.index, df['volume'], color='blue', alpha=0.5)
            plt.title('Volume')
            plt.ylabel('Volume')
            plt.grid(True)

            plt.tight_layout()

            # Save to BytesIO
            buf = BytesIO()
            plt.savefig(buf, format='png')
            buf.seek(0)
            plt.close()

            return buf

        except Exception as e:
            logger.error(f"Error generating chart: {e}")
            return None

    def get_current_price(self, symbol: str) -> float:
        """
        Get the current price for a symbol.

        Args:
            symbol: The trading pair symbol (e.g., "BTC/USDT")

        Returns:
            Current price as a float
        """
        try:
            # Get the latest OHLCV data
            df = self.exchange.get_ohlcv(symbol, "1m", 1)

            if df is None or df.empty:
                # If no data, try to get ticker directly
                ticker = self.exchange.get_ticker(symbol)
                if ticker and 'last' in ticker:
                    return float(ticker['last'])
                else:
                    raise ValueError(f"Could not get price data for {symbol}")

            # Return the latest close price
            return float(df['close'].iloc[-1])
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {e}")
            # Return a default value or raise an exception
            raise ValueError(f"Could not get current price for {symbol}: {e}")

    def calculate_volatility(self, symbol: str) -> float:
        """
        Calculate the volatility of a symbol based on recent price action.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT')

        Returns:
            float: Volatility as a decimal (e.g., 0.05 for 5% volatility)
        """
        try:
            # Get historical data for multiple timeframes
            timeframes = ['1h', '4h', '1d']
            volatilities = []

            for tf in timeframes:
                # Get OHLCV data
                df = self.exchange.get_ohlcv(symbol, tf, limit=20)

                if df is not None and not df.empty and len(df) >= 10:
                    # Calculate daily returns
                    df['returns'] = df['close'].pct_change()

                    # Calculate volatility as standard deviation of returns
                    volatility = df['returns'].std()
                    volatilities.append(volatility)

            # Average volatility across timeframes
            if volatilities:
                avg_volatility = sum(volatilities) / len(volatilities)
                return avg_volatility
            else:
                # Default volatility if calculation fails
                return 0.04  # 4% default volatility

        except Exception as e:
            logger.error(f"Error calculating volatility for {symbol}: {e}")
            return 0.04  # 4% default volatility

    def calculate_stop_loss(self, symbol: str, direction: str) -> float:
        """
        Calculate stop loss for a symbol based on market structure.

        Args:
            symbol: The trading pair symbol (e.g., "BTC/USDT")
            direction: "BUY" or "SELL"

        Returns:
            Stop loss price as a float
        """
        try:
            # Get OHLCV data
            df = self.exchange.get_ohlcv(symbol, "1h", 100)

            if df is None or df.empty:
                raise ValueError(f"Could not get OHLCV data for {symbol}")

            current_price = self.get_current_price(symbol)

            # Calculate ATR for volatility-based stop
            df['ATR'] = df['high'] - df['low']
            atr = df['ATR'].rolling(14).mean().iloc[-1]

            if direction == "BUY":
                # For buy signals, set stop loss below current price
                stop_loss = current_price - (atr * 2)
            else:
                # For sell signals, set stop loss above current price
                stop_loss = current_price + (atr * 2)

            return float(stop_loss)
        except Exception as e:
            logger.error(f"Error calculating stop loss for {symbol}: {e}")
            # Return a default stop loss (2% from current price)
            current_price = self.get_current_price(symbol)
            if direction == "BUY":
                return current_price * 0.98
            else:
                return current_price * 1.02

    def calculate_targets(self, symbol: str, direction: str) -> List[float]:
        """
        Calculate price targets for a symbol based on market structure.

        Args:
            symbol: The trading pair symbol (e.g., "BTC/USDT")
            direction: "BUY" or "SELL"

        Returns:
            List of target prices
        """
        try:
            # Get OHLCV data
            df = self.exchange.get_ohlcv(symbol, "1h", 100)

            if df is None or df.empty:
                raise ValueError(f"Could not get OHLCV data for {symbol}")

            current_price = self.get_current_price(symbol)
            stop_loss = self.calculate_stop_loss(symbol, direction)

            # Calculate risk (distance from entry to stop)
            risk = abs(current_price - stop_loss)

            if direction == "BUY":
                # For buy signals, set targets above current price
                targets = [
                    current_price + (risk * 1.5),  # Target 1: 1.5x risk
                    current_price + (risk * 2.5),  # Target 2: 2.5x risk
                    current_price + (risk * 4.0)   # Target 3: 4x risk
                ]
            else:
                # For sell signals, set targets below current price
                targets = [
                    current_price - (risk * 1.5),  # Target 1: 1.5x risk
                    current_price - (risk * 2.5),  # Target 2: 2.5x risk
                    current_price - (risk * 4.0)   # Target 3: 4x risk
                ]

            return [float(target) for target in targets]
        except Exception as e:
            logger.error(f"Error calculating targets for {symbol}: {e}")
            # Return default targets (5%, 10%, 15% from current price)
            current_price = self.get_current_price(symbol)
            if direction == "BUY":
                return [current_price * 1.05, current_price * 1.10, current_price * 1.15]
            else:
                return [current_price * 0.95, current_price * 0.90, current_price * 0.85]

    def analyze_symbol(self, symbol: str, market_type: str = "SPOT") -> Optional[Dict[str, bool]]:
        """
        Analyze a trading pair and generate a signal if conditions are met.

        Args:
            symbol: The trading pair symbol (e.g., "BTC/USDT")
            market_type: The market type ("SPOT" or "FUTURES")

        Returns:
            Dictionary of analysis results if conditions are met, None otherwise
        """
        try:
            # Get OHLCV data
            df = self.exchange.get_ohlcv(symbol, "1h", 100)

            if df is None:
                logger.warning(f"No data returned for {symbol}")
                return None

            if df.empty or len(df) < 50:
                logger.info(f"Insufficient data for {symbol}: {len(df) if not df.empty else 0} rows")
                return None

            # Check if DataFrame has required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.warning(f"Missing required columns for {symbol}: {missing_columns}")
                return None

            # Pre-calculate all indicators that will be needed to avoid KeyError issues
            try:
                # Calculate SMA values
                df['SMA_50'] = df['close'].rolling(window=50).mean()
                df['SMA_200'] = df['close'].rolling(window=200).mean()

                # Calculate Bollinger Bands
                df['BBM_5_2.0'] = df['close'].rolling(window=5).mean()
                df['BBU_5_2.0'] = df['BBM_5_2.0'] + (df['close'].rolling(window=5).std() * 2)
                df['BBL_5_2.0'] = df['BBM_5_2.0'] - (df['close'].rolling(window=5).std() * 2)

                # Calculate RSI
                delta = df['close'].diff()
                gain = delta.where(delta > 0, 0)
                loss = -delta.where(delta < 0, 0)
                avg_gain = gain.rolling(window=14).mean()
                avg_loss = loss.rolling(window=14).mean()
                rs = avg_gain / avg_loss
                df['RSI_14'] = 100 - (100 / (1 + rs))

                # Calculate MACD
                ema12 = df['close'].ewm(span=12, adjust=False).mean()
                ema26 = df['close'].ewm(span=26, adjust=False).mean()
                df['MACD_12_26_9'] = ema12 - ema26
                df['MACDs_12_26_9'] = df['MACD_12_26_9'].ewm(span=9, adjust=False).mean()

                # Calculate ATR
                high_low = df['high'] - df['low']
                high_close = (df['high'] - df['close'].shift()).abs()
                low_close = (df['low'] - df['close'].shift()).abs()
                ranges = pd.concat([high_low, high_close, low_close], axis=1)
                true_range = ranges.max(axis=1)
                df['ATRr_14'] = true_range.rolling(14).mean()
            except Exception as e:
                logger.warning(f"Error pre-calculating indicators for {symbol}: {e}")
                # Calculate essential indicators manually
                try:
                    # Calculate SMA values manually
                    df['SMA_50'] = df['close'].rolling(window=50).mean()
                    df['SMA_200'] = df['close'].rolling(window=200).mean()

                    # Calculate Bollinger Bands manually
                    df['BBM_5_2.0'] = df['close'].rolling(window=5).mean()
                    df['BBU_5_2.0'] = df['BBM_5_2.0'] + (df['close'].rolling(window=5).std() * 2)
                    df['BBL_5_2.0'] = df['BBM_5_2.0'] - (df['close'].rolling(window=5).std() * 2)
                except Exception as e2:
                    logger.error(f"Critical error calculating essential indicators for {symbol}: {e2}")
                    return None

            # Skip high-priced tokens for spot trading
            if market_type == "SPOT":
                current_price = df['close'].iloc[-1]
                if current_price > 20:
                    logger.info(f"Skipping high-priced token {symbol} for SPOT trading")
                    return None

            # Create a dictionary to store analysis results
            analysis_result = {
                "ICT condition": False,
                "SMC condition": False,
                "PA condition": False,
                "S/R condition": False,
                "Pattern recognition condition": False
            }

            # Analyze trading concepts
            concepts_met = []
            explanations = []

            # ICT analysis
            ict_met, ict_explanation = self.analyze_ict(df)
            if ict_met:
                concepts_met.append("ICT")
                explanations.append(ict_explanation)
                analysis_result["ICT condition"] = True

            # SMC analysis
            smc_met, smc_explanation = self.analyze_smc(df)
            if smc_met:
                concepts_met.append("SMC")
                explanations.append(smc_explanation)
                analysis_result["SMC condition"] = True

            # Price Action analysis
            pa_met, pa_explanation = self.analyze_price_action(df)
            if pa_met:
                concepts_met.append("PA")
                explanations.append(pa_explanation)
                analysis_result["PA condition"] = True

            # Support/Resistance analysis
            sr_met, sr_explanation = self.analyze_support_resistance(df)
            if sr_met:
                concepts_met.append("S/R")
                explanations.append(sr_explanation)
                analysis_result["S/R condition"] = True

            # Pattern analysis
            pattern_met, pattern_explanation = self.analyze_patterns(df)
            if pattern_met:
                concepts_met.append("Pattern Recognition")
                explanations.append(pattern_explanation)
                analysis_result["Pattern recognition condition"] = True

            # Log which concepts were met for this symbol
            if concepts_met:
                logger.info(f"Symbol {symbol} in {market_type} market met the following concepts: {', '.join(concepts_met)}")

            # Return the analysis results
            return analysis_result

        except Exception as e:
            logger.error(f"Error analyzing symbol {symbol}: {e}")
            return None

    def find_trade_signals(self) -> Dict[str, List[Dict[str, bool]]]:
        """
        Find trading signals across all viable pairs.

        Returns:
            Dictionary with market types as keys and lists of analysis results as values
        """
        signals = {
            "SPOT": [],
            "FUTURES": []
        }

        # Get viable trading pairs
        viable_pairs = self.exchange.get_viable_trading_pairs()

        # Analyze each pair for both market types
        for symbol in viable_pairs:
            for market_type in ["SPOT", "FUTURES"]:
                analysis_result = self.analyze_symbol(symbol, market_type)
                if analysis_result:
                    # Add symbol and market type to the result
                    analysis_result["symbol"] = symbol
                    analysis_result["market_type"] = market_type
                    signals[market_type].append(analysis_result)

        return signals
